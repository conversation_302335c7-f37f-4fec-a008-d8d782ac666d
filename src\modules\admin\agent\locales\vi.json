{"agent": {"management": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý tổng quan hệ thống <PERSON>"}, "notification": {"createSuccess": "{{entityName}} đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "updateSuccess": "{{entityName}} đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "deleteSuccess": "{{entityName}} đ<PERSON> đư<PERSON>c xóa thành công", "restoreSuccess": "{{entityName}} đ<PERSON> đ<PERSON><PERSON><PERSON> khôi phục thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo {{entityName}}", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật {{entityName}}", "deleteError": "Có lỗi xảy ra khi xóa {{entityName}}", "restoreError": "<PERSON><PERSON> lỗi xảy ra khi khôi phục {{entityName}}", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách {{entityName}}", "uploadSuccess": "<PERSON><PERSON><PERSON> lên thành công", "uploadSuccessWithName": "<PERSON><PERSON><PERSON> lên {{fileName}} thành công", "uploadError": "<PERSON><PERSON> lỗi xảy ra khi tải lên", "validationError": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "permissionError": "<PERSON><PERSON>n không có quyền thực hiện thao tác này", "networkError": "Lỗi kết nối mạng. <PERSON><PERSON> lòng thử lại", "processing": "<PERSON><PERSON> {{action}}..."}, "rank": {"title": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> h<PERSON>", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "addRank": "<PERSON><PERSON><PERSON><PERSON>", "editRank": "Chỉnh s<PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm cấp bậc...", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cấp bậc phù hợp với từ khóa tìm kiếm.", "createFirst": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> tiên", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "createSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "createSuccessMessage": "<PERSON><PERSON><PERSON> bậc agent đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "updateSuccessMessage": "<PERSON><PERSON><PERSON> bậc agent đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "editAction": "Chỉnh sửa", "deleteAction": "Xóa", "confirmDelete": "<PERSON><PERSON><PERSON> nhận x<PERSON><PERSON> cấp bậc", "deleteMessage": "Bạn có chắc chắn muốn xóa cấp bậc này không? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON><PERSON> c<PERSON>p b<PERSON>c thành công", "deleteError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi xóa cấp bậc", "memories": "Bộ nhớ", "list": {"title": "<PERSON><PERSON> <PERSON><PERSON>", "noRanks": "<PERSON><PERSON><PERSON><PERSON> có cấp b<PERSON>c n<PERSON>o", "noRanksDescription": "<PERSON><PERSON><PERSON> tại chưa có cấp bậc nào trong hệ thống.", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách cấp bậc. <PERSON><PERSON> lòng thử lại.", "loading": "<PERSON><PERSON> tải danh s<PERSON>ch cấp bậc...", "refreshing": "<PERSON><PERSON> làm mới dữ liệu..."}, "form": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON><PERSON> c<PERSON>p bậc", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cấp bậc", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cấp bậc", "expRange": "<PERSON><PERSON><PERSON><PERSON>h nghiệm", "minExp": "<PERSON><PERSON> nghiệm tối thiểu", "maxExp": "<PERSON><PERSON> nghiệm tối đa", "badge": "<PERSON><PERSON>", "badgeUpload": "<PERSON><PERSON><PERSON> lên huy hi<PERSON>u", "badgeHelp": "Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)", "currentBadge": "<PERSON><PERSON> <PERSON><PERSON><PERSON> hi<PERSON> tại", "currentBadgeNote": "<PERSON><PERSON><PERSON> lên <PERSON>nh mới để thay thế", "active": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "creating": "<PERSON><PERSON> t<PERSON>o cấp bậc...", "createSuccess": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> b<PERSON>c thành công", "createError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi tạo cấp bậc", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật cấp bậc"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> b<PERSON><PERSON> là bắt buộc", "descriptionRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "minExpInvalid": "<PERSON><PERSON> nghiệm tối thiểu phải >= 0", "minExpInteger": "<PERSON><PERSON> nghiệm tối thiểu phải là số nguyên", "maxExpInvalid": "<PERSON><PERSON> nghiệm tối đa phải > 0", "maxExpInteger": "<PERSON><PERSON> nghiệm tối đa phải là số nguyên", "expRangeInvalid": "<PERSON><PERSON> nghiệm tối đa phải lớn hơn kinh nghiệm tối thiểu", "expRangeOverlap": "<PERSON><PERSON><PERSON><PERSON> kinh nghiệm chồng chéo với khoảng kinh nghiệm của cấp b<PERSON><PERSON> kh<PERSON>c"}, "edit": {"notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cấp bậc"}, "sort": {"name": "<PERSON><PERSON><PERSON>", "minExp": "<PERSON><PERSON> nghiệm tối thiểu", "maxExp": "<PERSON><PERSON> nghiệm tối đa", "createdAt": "<PERSON><PERSON><PERSON>"}}, "system": {"mcpSystem": {"title": "<PERSON><PERSON> th<PERSON>ng MCP", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> <PERSON> thống MCP", "pageDescription": "<PERSON><PERSON><PERSON><PERSON> lý c<PERSON>c hệ thống Model Context Protocol cho AI agents c<PERSON><PERSON> b<PERSON>n", "subtitle": "<PERSON><PERSON><PERSON><PERSON> lý c<PERSON>c hệ thống Model Context Protocol của bạn", "loading": "<PERSON><PERSON> tả<PERSON> thống MCP...", "noMCPSystems": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> <PERSON> thống MCP", "noMCPSystemsDescription": "<PERSON><PERSON><PERSON> có hệ thống MCP n<PERSON><PERSON> đ<PERSON><PERSON><PERSON> tạo. <PERSON><PERSON><PERSON> hệ thống MCP đầu tiên để bắt đầu.", "addFirst": "<PERSON><PERSON><PERSON><PERSON> thống MCP đ<PERSON><PERSON> tiên", "addNew": "<PERSON><PERSON><PERSON><PERSON>ng <PERSON>P", "edit": "Chỉnh sửa", "delete": "Xóa", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "configInvalid": "<PERSON><PERSON><PERSON> hình không hợp lệ để kiểm tra", "transport": "<PERSON><PERSON><PERSON> th<PERSON>", "updated": "<PERSON><PERSON><PERSON>", "by": "Bởi", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "deleteWarning": "Bạn có chắc chắn muốn xóa Hệ thống MCP này? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON><PERSON> thống MCP thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa <PERSON> thống MCP", "testSuccess": "<PERSON><PERSON><PERSON> tra kết nối thành công", "testError": "<PERSON><PERSON><PERSON> tra kết nối thất bại", "totalSystems": "<PERSON><PERSON><PERSON> số hệ thống", "httpSystems": "<PERSON><PERSON> thống HTTP", "sseSystems": "<PERSON><PERSON> thống SSE", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON> hệ thống MCP...", "manage": "<PERSON><PERSON><PERSON><PERSON> lý", "systemMCPTitle": "System MCP", "systemMCPSubtitle": "Admin MCP system management endpoints", "form": {"addDescription": "<PERSON><PERSON><PERSON> một hệ thống Model Context Protocol mới", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "nameServer": "<PERSON><PERSON><PERSON> m<PERSON> chủ", "nameServerPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên m<PERSON> chủ MCP", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho hệ thống MCP", "config": "<PERSON><PERSON><PERSON> h<PERSON>nh", "transport": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> vận chuy<PERSON>n", "selectTransport": "<PERSON><PERSON><PERSON> giao thức vận chuyển", "httpDescription": "<PERSON><PERSON><PERSON>c HTTP ti<PERSON><PERSON> ch<PERSON>n", "sseDescription": "Server-Sent Events", "url": "URL", "urlPlaceholder": "Nhập URL của máy chủ MCP", "headers": "Headers", "addHeader": "<PERSON><PERSON><PERSON><PERSON>", "headerName": "<PERSON><PERSON><PERSON>", "headerValue": "<PERSON><PERSON><PERSON> trị", "headerNamePlaceholder": "<PERSON><PERSON><PERSON> header", "headerValuePlaceholder": "<PERSON><PERSON><PERSON> trị header", "removeHeader": "Xóa header", "create": "<PERSON><PERSON><PERSON> thống MCP", "update": "<PERSON><PERSON><PERSON><PERSON> thống MCP", "editDescription": "<PERSON><PERSON><PERSON> nh<PERSON>t c<PERSON>u hình hệ thống Model Context Protocol", "loading": "<PERSON><PERSON> tải thông tin <PERSON> thống MCP...", "validation": {"nameServerRequired": "<PERSON><PERSON><PERSON> m<PERSON><PERSON> chủ là bắt buộc", "nameServerMinLength": "<PERSON>ên máy chủ phải có ít nhất 2 ký tự", "descriptionRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "transportRequired": "<PERSON><PERSON><PERSON><PERSON> vận chuyển là bắt buộc", "urlRequired": "URL là bắt buộc", "urlInvalid": "URL không hợp lệ", "headerNameRequired": "Tên header l<PERSON> b<PERSON><PERSON> bu<PERSON>c", "headerValueRequired": "<PERSON><PERSON><PERSON> trị header l<PERSON> b<PERSON><PERSON> bu<PERSON><PERSON>"}, "createSuccess": "<PERSON><PERSON><PERSON> thống MCP thành công", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo <PERSON> thống MCP", "updateSuccess": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> thống MCP thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nh<PERSON>t <PERSON><PERSON> thống MCP", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải thông tin <PERSON>ệ thống MCP"}}, "title": "Q<PERSON>ản lý System Agent", "description": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> h<PERSON> thống", "pageTitle": "Q<PERSON>ản lý System Agent", "addAgent": "Thêm Agent mới", "editAgent": "Chỉnh sửa Agent System", "searchPlaceholder": "<PERSON><PERSON><PERSON> Agent...", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy Agent phù hợp với từ khóa tìm kiếm.", "createFirst": "Tạo System Agent đ<PERSON>u tiên", "viewTrash": "<PERSON><PERSON> th<PERSON>", "backToMain": "Quay lại danh s<PERSON>ch ch<PERSON>h", "createSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "createSuccessMessage": "Agent system đã đư<PERSON><PERSON> tạo thành công", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "cancel": "<PERSON><PERSON><PERSON>", "updateAgent": "<PERSON><PERSON><PERSON>", "updateSuccessMessage": "Agent system đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "mcpConfig": {"title": "MCP Systems", "loadingMCPSystems": "Đang tải MCP systems...", "noMCPSystemsSelected": "Chưa có MCP system nào đ<PERSON><PERSON><PERSON> chọn", "addMCPSystem": "Thêm MCP System", "removeMCPSystem": "Xóa MCP System", "confirmDeleteMCPSystem": "Bạn có chắc chắn muốn xóa MCP system \"{{mcpSystemName}}\" khỏi loại agent không?", "deleteMCPSystemWarning": "<PERSON><PERSON><PERSON> động này không thể hoàn tác."}, "mcpSlideIn": {"title": "Chọn MCP Systems", "mcpSystem": "MCP System", "description": "<PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "updateMCPsSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t MCP Systems thành công", "updateMCPsError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật MCP Systems", "addMCPsToListSuccess": "Thêm MCP Systems vào danh sách thành công", "cannotSaveInThisMode": "<PERSON><PERSON><PERSON><PERSON> thể lưu ở chế độ này"}}, "template": {"title": "Agent Template", "description": "<PERSON><PERSON><PERSON><PERSON> lý các template agent có sẵn", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "noTemplates": "Chưa có template nào", "noTemplatesDescription": "<PERSON><PERSON><PERSON> tại chưa có template nào trong hệ thống.", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy template nào phù hợp", "refreshing": "<PERSON><PERSON> làm mới dữ liệu...", "viewTrash": "<PERSON><PERSON> th<PERSON>", "backToMain": "Quay lại danh s<PERSON>ch ch<PERSON>h", "selectType": "<PERSON><PERSON><PERSON>", "selectTypeDescription": "Chọn loại agent phù hợp với nhu cầu của bạn. Mỗi loại agent có khả năng và đặc điểm khác nhau.", "createTitle": "Tạo Agent", "editTitle": "Chỉnh sửa Agent Template", "create": {"title": "Tạo Agent Template"}, "profile": {"title": "<PERSON><PERSON><PERSON><PERSON> tin Hồ sơ", "dateOfBirth": "<PERSON><PERSON><PERSON>", "dateOfBirthPlaceholder": "<PERSON><PERSON><PERSON> ng<PERSON> sinh", "gender": {"label": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "placeholder": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> t<PERSON>h", "male": "Nam", "female": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "education": {"label": "<PERSON><PERSON><PERSON><PERSON> độ học vấn", "placeholder": "<PERSON><PERSON><PERSON> trình độ học vấn", "highSchool": "<PERSON><PERSON> học", "college": "<PERSON> đẳng", "university": "<PERSON><PERSON><PERSON>", "master": "<PERSON><PERSON><PERSON><PERSON> sĩ", "phd": "<PERSON><PERSON><PERSON><PERSON> sĩ"}, "languages": "<PERSON><PERSON><PERSON>", "languagesPlaceholder": "<PERSON><PERSON><PERSON> ngôn ngữ", "nations": "Quốc gia", "nationsPlaceholder": "<PERSON><PERSON><PERSON> quốc gia", "position": "<PERSON><PERSON> trí", "positionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> vị trí", "skills": "<PERSON><PERSON> n<PERSON>ng", "skillsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kỹ năng và nhấn Enter", "personality": "<PERSON><PERSON><PERSON>", "personalityPlaceholder": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> c<PERSON>ch và nhấn Enter"}, "memories": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hình các ký <PERSON>c mà agent sẽ sử dụng để cung cấp phản hồi tốt hơn và duy trì ngữ cảnh", "noMemories": "<PERSON><PERSON><PERSON> có ký <PERSON>c nào đ<PERSON><PERSON><PERSON> cấu hình", "addMemory": "<PERSON><PERSON><PERSON><PERSON>", "editMemory": "Chỉnh s<PERSON><PERSON>", "memoryTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "titlePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tiêu đề ký <PERSON>c", "reason": "Lý do", "reasonPlaceholder": "Nhập lý do cho ký <PERSON>c này", "content": "<PERSON><PERSON>i dung", "contentPlaceholder": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung ký <PERSON>c", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "confirmDelete": "Bạn có chắc chắn muốn xóa ký <PERSON>c nà<PERSON>?", "deleteSuccess": "<PERSON><PERSON><PERSON> k<PERSON> thành công", "deleteError": "Lỗi khi xóa ký <PERSON>c", "saveSuccess": "<PERSON><PERSON><PERSON> thành công", "saveError": "Lỗi khi lưu k<PERSON>", "validation": {"titleRequired": "Ti<PERSON><PERSON> đề ký <PERSON>c là bắt buộc", "reasonRequired": "Lý do là bắt buộc", "contentRequired": "Nội dung ký <PERSON>c là bắt buộc"}}, "conversion": {"title": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> đổi", "description": "<PERSON><PERSON><PERSON> hình các trường dữ liệu sẽ được thu thập từ người dùng trong quá trình trò chuyện", "noFields": "<PERSON><PERSON><PERSON> có trường chuyển đổi nào đ<PERSON><PERSON><PERSON> cấu hình", "addField": "<PERSON><PERSON><PERSON><PERSON> đổi", "editField": "Chỉnh sửa Trường <PERSON>n đổi", "fieldName": "<PERSON><PERSON><PERSON>", "fieldNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên trườ<PERSON>", "fieldDescription": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả trường", "fieldType": "Loại Trường", "dataType": "<PERSON><PERSON><PERSON> liệu", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "active": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "confirmDelete": "Bạn có chắc chắn muốn xóa trường này?", "deleteSuccess": "<PERSON><PERSON><PERSON> trư<PERSON>ng thành công", "deleteError": "Lỗi khi xóa trường", "saveSuccess": "<PERSON><PERSON><PERSON> trư<PERSON><PERSON> thành công", "saveError": "Lỗi khi lưu trường", "type": {"string": "Chuỗi", "number": "Số", "boolean": "Boolean", "arrayString": "Mảng Chuỗi", "arrayNumber": "Mảng Số", "enum": "Enum"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> là bắt buộc", "descriptionRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "typeRequired": "Loại trường là bắt buộc"}}, "name": "<PERSON><PERSON>n <PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên agent", "provider": "<PERSON><PERSON><PERSON> cung cấp", "model": "Model", "selectModel": "<PERSON><PERSON><PERSON> model", "strategy": "<PERSON><PERSON><PERSON>", "selectStrategy": "<PERSON><PERSON><PERSON> ch<PERSON>", "modelConfig": "<PERSON><PERSON><PERSON>", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "instruction": "Hướng dẫn", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> dẫn cho model...", "createSuccess": "Tạo agent template thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo agent template", "updateSuccess": "<PERSON><PERSON><PERSON> nhật agent template thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật agent template", "loading": "<PERSON><PERSON> t<PERSON> agent template...", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải agent template", "uploadingAvatar": "<PERSON><PERSON> tải lên avatar...", "uploadAvatarSuccess": "<PERSON><PERSON><PERSON> lên avatar thành công", "uploadAvatarError": "<PERSON><PERSON> lỗi xảy ra khi tải lên avatar", "isForSale": "Hỗ trợ bán hàng", "isForSaleDescription": {"enabled": "Agent n<PERSON><PERSON> hỗ trợ bán hàng và có thể được sử dụng cho các hoạt động thương mại", "disabled": "Agent <PERSON><PERSON><PERSON>ô<PERSON> hỗ trợ bán hàng, chỉ dành cho mục đích hỗ trợ và tư vấn"}, "validation": {"nameRequired": "Tên agent <PERSON><PERSON><PERSON><PERSON>", "modelRequired": "<PERSON><PERSON><PERSON> model <PERSON><PERSON> b<PERSON><PERSON> bu<PERSON><PERSON>", "strategyRequired": "<PERSON><PERSON><PERSON> chọn chiến lư<PERSON><PERSON> là bắt buộc"}, "card": {"model": "Model", "forSale": "Hỗ trợ bán", "supported": "Có hỗ trợ", "notSupported": "Không hỗ trợ", "delete": "Xóa", "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "memories": "Bộ nhớ", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON>", "deleteMessage": "Bạn có chắc chắn muốn xóa template này không? Hành động này không thể hoàn tác.", "deleteSuccess": "Xóa template thành công", "deleteError": "Có lỗi xảy ra khi xóa template", "restoreSuccess": "<PERSON><PERSON><PERSON><PERSON> phục template thành công", "restoreError": "Có lỗi xảy ra khi khôi phục template"}, "trash": {"title": "<PERSON><PERSON><PERSON><PERSON> - Agent Template", "noTemplates": "<PERSON><PERSON><PERSON>ng có template nào trong thùng rác", "noTemplatesDescription": "Th<PERSON><PERSON> rác trống. Các template đã xóa sẽ xuất hiện ở đây.", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy template nào phù hợp", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách template đã xóa"}}, "type": {"title": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> Agent", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "addType": "<PERSON><PERSON><PERSON><PERSON> Agent mới", "editType": "Chỉnh s<PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON> lo<PERSON> agent...", "createFirst": "<PERSON><PERSON><PERSON> Agent đ<PERSON><PERSON> tiên", "createSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "createSuccessMessage": "Loại agent đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "updateSuccessMessage": "Loại agent đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "cancel": "<PERSON><PERSON><PERSON>", "updateType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewTrash": "<PERSON><PERSON> th<PERSON>", "backToMain": "Quay lại danh s<PERSON>ch ch<PERSON>h", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy loại agent phù hợp với từ khóa tìm kiếm.", "deleteConfirmTitle": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON> lo<PERSON> agent", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa loại agent nà<PERSON>? Hành động này sẽ chuyển loại agent vào thùng rác và có thể khôi phục lại.", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteSuccessMessage": "Loại agent đ<PERSON> đ<PERSON><PERSON><PERSON> xóa thành công", "restoreSuccess": "<PERSON><PERSON><PERSON><PERSON> phục thành công", "restoreSuccessMessage": "Loại agent đ<PERSON> đ<PERSON><PERSON><PERSON> khôi phục thành công", "selectTypeToDelete": "Chọn loại agent đ<PERSON>a", "selectTypeToDeleteDescription": "Chọn loại agent mà bạn muốn xóa. Loại agent sẽ được chuyển vào thùng rác và có thể khôi phục lại.", "deleteWithMigration": "<PERSON>óa với chuyển đổi", "deleteWithMigrationDescription": "Xóa loại agent v<PERSON> chuyển tất cả agents thu<PERSON><PERSON> lo<PERSON> này sang loại mới đư<PERSON><PERSON> chọn.", "newTypeAgent": "Loại agent mới", "selectNewType": "Chọn loại agent mới", "selectNewTypeDescription": "Chọn loại agent mớ<PERSON> để chuyển đổi các agents hi<PERSON><PERSON> tại.", "noAvailableTypes": "<PERSON>hông có loại agent n<PERSON><PERSON> kh<PERSON>c để chuyển đổi. Bạn chỉ có thể xóa mà không chuyển đổi.", "deleteOnly": "Chỉ xóa", "deleteError": "<PERSON><PERSON> lỗi xảy ra khi xóa loại agent", "restoreError": "<PERSON><PERSON> lỗi xảy ra khi khôi phục loại agent"}, "strategy": {"title": "<PERSON><PERSON><PERSON>n lý Agent Strategy", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiế<PERSON> l<PERSON> agent", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> l<PERSON>", "addStrategy": "<PERSON><PERSON><PERSON><PERSON> mới", "editStrategy": "Chỉnh s<PERSON>a <PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm chiến l<PERSON>...", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chiến lư<PERSON><PERSON> phù hợp với từ khóa tìm kiếm.", "createFirst": "<PERSON><PERSON><PERSON> l<PERSON> Agent đ<PERSON><PERSON> tiên", "createSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "createSuccessMessage": "Chiến lược agent đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "updateSuccessMessage": "Chiến lược agent đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "cancel": "<PERSON><PERSON><PERSON>", "updateStrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewTrash": "<PERSON><PERSON> th<PERSON>", "backToMain": "Quay lại danh s<PERSON>ch ch<PERSON>h", "list": {"title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "noStrategies": "<PERSON><PERSON><PERSON><PERSON> có chiến l<PERSON> n<PERSON>o", "noStrategiesDescription": "<PERSON><PERSON><PERSON> tại chưa có chiến lư<PERSON><PERSON> nào trong hệ thống.", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách chiến lư<PERSON><PERSON>. <PERSON><PERSON> lòng thử lại.", "loading": "<PERSON><PERSON> tải danh s<PERSON>ch chiến l<PERSON>...", "refreshing": "<PERSON><PERSON> làm mới dữ liệu..."}, "card": {"edit": "Chỉnh sửa", "delete": "Xóa", "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "memories": "Bộ nhớ", "confirmDelete": "<PERSON><PERSON><PERSON> nh<PERSON>n x<PERSON><PERSON> l<PERSON>", "deleteMessage": "Bạn có chắc chắn muốn xóa chiến lược này không? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> thành công", "deleteError": "<PERSON><PERSON> lỗi xảy ra khi xóa chiến lư<PERSON>c", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t chi<PERSON>n lư<PERSON><PERSON> thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật chiến lược", "restoreSuccess": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> chiến lư<PERSON><PERSON> thành công", "restoreError": "<PERSON><PERSON> lỗi xảy ra khi khôi phục chiến lư<PERSON>c"}, "form": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên chi<PERSON>", "avatar": "Avatar", "avatarUpload": "<PERSON><PERSON><PERSON> lên avatar", "avatarHelp": "Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)", "modelConfig": "<PERSON><PERSON><PERSON>", "temperature": "Temperature", "maxTokens": "<PERSON>", "instruction": "Hướng dẫn", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> hướ<PERSON> dẫn cho chiến lược", "content": "<PERSON><PERSON><PERSON> dung c<PERSON>c b<PERSON>", "contentStep": "<PERSON><PERSON><PERSON><PERSON> {step}", "contentPlaceholder": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i dung bước {step}", "addStep": "<PERSON><PERSON><PERSON><PERSON>", "removeStep": "<PERSON><PERSON><PERSON>", "exampleDefault": "<PERSON><PERSON> dụ mặc định", "exampleStep": "<PERSON><PERSON> dụ {step}", "examplePlaceholder": "<PERSON><PERSON><PERSON><PERSON> ví dụ cho bước {step}", "addExample": "Thêm ví dụ", "removeExample": "Xóa ví dụ", "systemModel": "System Model", "provider": "Loại Provider", "selectProvider": "Chọn provider", "selectProviderFirst": "V<PERSON> lòng chọn provider trước", "model": "Model", "selectSystemModel": "Chọn system model", "selectModel": "<PERSON><PERSON><PERSON> model", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "creating": "<PERSON><PERSON> tạo chi<PERSON> l<PERSON>...", "updating": "<PERSON><PERSON> cập nhật chiế<PERSON> l<PERSON>...", "createSuccess": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo chiến lư<PERSON>c", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t chi<PERSON>n lư<PERSON><PERSON> thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật chiến lược", "uploadingAvatar": "<PERSON><PERSON> tải lên avatar...", "uploadAvatarSuccess": "<PERSON><PERSON><PERSON> lên avatar thành công", "uploadAvatarError": "<PERSON><PERSON> lỗi xảy ra khi tải lên avatar", "loadSystemModelsError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách system models", "currentAvatar": "Avatar hi<PERSON>n tại"}, "trash": {"title": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "noStrategies": "<PERSON><PERSON><PERSON><PERSON> có chiến lư<PERSON>c nào trong thùng rác", "noStrategiesDescription": "<PERSON>h<PERSON><PERSON> rác trống. <PERSON><PERSON><PERSON> chiến lược đã xóa sẽ xuất hiện ở đây.", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chiến lư<PERSON><PERSON> nào phù hợp", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách chiến lư<PERSON><PERSON> đã xóa"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> là bắt buộc", "instructionRequired": "Hướng dẫn là bắt buộc", "systemModelRequired": "System model l<PERSON> b<PERSON><PERSON> bu<PERSON>c", "providerRequired": "<PERSON>ui lòng chọn provider", "contentRequired": "<PERSON>ộ<PERSON> dung các b<PERSON><PERSON><PERSON> là bắt buộc", "contentStepRequired": "<PERSON><PERSON><PERSON> dung bước {step} là bắt buộc", "exampleRequired": "<PERSON><PERSON> dụ mặc định là bắt buộc", "exampleStepRequired": "<PERSON><PERSON> dụ bước {step} là bắt buộc"}}, "supervisor": {"title": "<PERSON><PERSON><PERSON><PERSON> lý Agent Supervisor", "description": "<PERSON><PERSON><PERSON><PERSON> lý Agent Supervisor", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> lý Agent Supervisor", "addSupervisor": "Thêm Agent Supervisor mới", "editSupervisor": "Chỉnh sửa Agent Supervisor", "searchPlaceholder": "<PERSON><PERSON><PERSON> Agent Supervisor...", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy Agent Supervisor phù hợp với từ khóa tìm kiếm.", "createFirst": "Tạo Agent Supervisor đ<PERSON><PERSON> tiên", "viewTrash": "<PERSON><PERSON> th<PERSON>", "backToMain": "Quay lại danh s<PERSON>ch ch<PERSON>h", "createSuccess": "Tạo Agent Supervisor thành công", "updateSuccess": "<PERSON><PERSON><PERSON>t Agent Supervisor thành công", "deleteSuccess": "Xóa Agent Supervisor thành công", "restoreSuccess": "<PERSON><PERSON><PERSON><PERSON> phục Agent Supervisor thành công", "createError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi tạo Agent Supervisor", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật Agent Supervisor", "deleteError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi xóa Agent Supervisor", "restoreError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi khôi phục Agent Supervisor", "toggleActiveSuccess": "Thay đổi trạng thái thành công", "toggleActiveError": "<PERSON><PERSON> lỗi xảy ra khi thay đổi trạng thái", "list": {"title": "<PERSON><PERSON> Agent Supervisor", "noSupervisors": "Không có Agent Supervisor nào", "noSupervisorsDescription": "<PERSON><PERSON><PERSON> tại chưa có Agent Supervisor nà<PERSON> trong hệ thống.", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Agent Supervisor. <PERSON><PERSON> lòng thử lại.", "loading": "<PERSON><PERSON> tải danh s<PERSON>ch Agent Supervisor...", "refreshing": "<PERSON><PERSON> làm mới dữ liệu..."}, "card": {"model": "Model", "provider": "Provider", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "edit": "Chỉnh sửa", "delete": "Xóa", "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a Agent Supervisor", "deleteMessage": "Bạn có chắc chắn muốn xóa Agent Supervisor này không? Hành động này không thể hoàn tác.", "deleteSuccess": "Xóa Agent Supervisor thành công", "deleteError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi xóa Agent Supervisor", "updateSuccess": "<PERSON><PERSON><PERSON>t Agent Supervisor thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật Agent Supervisor", "restoreSuccess": "<PERSON><PERSON><PERSON><PERSON> phục Agent Supervisor thành công", "restoreError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi khôi phục Agent Supervisor"}, "form": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON>n Agent Supervisor", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên Agent Supervisor", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả Agent Supervisor", "instruction": "Hướng dẫn", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> dẫn cho Agent Supervisor", "avatar": "Avatar", "avatarUpload": "<PERSON><PERSON><PERSON> lên avatar", "avatarHelp": "Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)", "currentAvatar": "Avatar hi<PERSON>n tại", "currentAvatarNote": "<PERSON><PERSON><PERSON> lên <PERSON>nh mới để thay thế", "provider": "Loại Provider", "selectProvider": "Chọn provider", "resources": "<PERSON><PERSON><PERSON>", "model": "Model", "selectModel": "<PERSON><PERSON><PERSON> model", "selectModelFirst": "<PERSON><PERSON> lò<PERSON> chọn model đ<PERSON> cấu hình parameters", "modelConfig": "<PERSON><PERSON><PERSON>", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "mcpSystems": "MCP Systems", "knowledgeFiles": "Knowledge Files", "create": "Tạo Agent Supervisor", "update": "<PERSON><PERSON><PERSON>t Agent Supervisor", "creating": "<PERSON><PERSON> tạo Agent Supervisor...", "updating": "<PERSON><PERSON> cập nh<PERSON>t Agent Supervisor...", "createSuccess": "Tạo Agent Supervisor thành công", "createError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi tạo Agent Supervisor", "updateSuccess": "<PERSON><PERSON><PERSON>t Agent Supervisor thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật Agent Supervisor", "uploadingAvatar": "<PERSON><PERSON> tải lên avatar...", "uploadAvatarSuccess": "<PERSON><PERSON><PERSON> lên avatar thành công", "uploadAvatarError": "<PERSON><PERSON> lỗi xảy ra khi tải lên avatar", "loadSystemModelsError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách system models", "validation": {"nameRequired": "Tên Agent Supervisor l<PERSON> b<PERSON><PERSON> bu<PERSON><PERSON>", "descriptionRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "instructionRequired": "Hướng dẫn là bắt buộc", "modelRequired": "<PERSON> <PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>"}}, "fileConfig": {"title": "Knowledge Files", "loadingFiles": "<PERSON><PERSON> t<PERSON>i Knowledge Files...", "noFilesSelected": "Chưa có <PERSON> File nào đ<PERSON><PERSON><PERSON> chọn", "addFile": "Thêm Knowledge File", "removeFile": "Xóa Knowledge File", "confirmDeleteFile": "<PERSON><PERSON>n có chắc chắn muốn xóa Knowledge File \"{{fileName}}\" khỏi Agent Supervisor không?", "deleteFileWarning": "<PERSON><PERSON><PERSON> động này không thể hoàn tác."}, "fileSlideIn": {"title": "Chọn Knowledge Files", "fileName": "<PERSON><PERSON><PERSON>", "extension": "<PERSON><PERSON><PERSON> d<PERSON>ng", "storage": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "updateFilesSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t Knowledge Files thành công", "updateFilesError": "<PERSON><PERSON><PERSON><PERSON> thể cập nh<PERSON>t Knowledge Files", "addFilesToListSuccess": "Thêm Knowledge Files vào danh sách thành công", "cannotSaveInThisMode": "<PERSON><PERSON><PERSON><PERSON> thể lưu ở chế độ này"}, "trash": {"title": "<PERSON><PERSON><PERSON><PERSON> - Agent Supervisor", "noSupervisors": "<PERSON><PERSON>ông có Agent Supervisor nào trong thùng rác", "noSupervisorsDescription": "<PERSON><PERSON><PERSON><PERSON> rác trống. Các Agent Supervisor đã xóa sẽ xuất hiện ở đây.", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy Agent Supervisor nào phù hợp", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Agent Supervisor đ<PERSON> xóa"}}, "user": {"title": "Admin - Agent User", "description": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> ng<PERSON><PERSON> dùng"}, "trash": {"title": "Thùng rác - Loại Agent", "noAgents": "<PERSON><PERSON><PERSON>ng có loại agent nào trong thùng rác", "noAgentsDescription": "<PERSON>h<PERSON><PERSON> rác trống. Các lo<PERSON>i agent đã xóa sẽ xuất hiện ở đây.", "restoreAgent": "Khôi phục lo<PERSON>i agent", "permanentDelete": "<PERSON>ó<PERSON> v<PERSON><PERSON> vi<PERSON>n"}, "list": {"title": "<PERSON><PERSON>", "noTypes": "<PERSON><PERSON><PERSON>ng có loại agent nào", "noTypesDescription": "<PERSON><PERSON><PERSON> tại chưa có loại agent n<PERSON><PERSON> trong hệ thống.", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách loại agent. <PERSON><PERSON> lòng thử lại.", "loading": "<PERSON><PERSON> tải danh s<PERSON>ch lo<PERSON> agent...", "refreshing": "<PERSON><PERSON> làm mới dữ liệu..."}, "card": {"edit": "Chỉnh sửa", "delete": "Xóa", "cancel": "<PERSON><PERSON><PERSON>", "memories": "Memories", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON>", "deleteMessage": "Bạn có chắc chắn muốn xóa loại agent nà<PERSON> không? Hành động này không thể hoàn tác.", "deleteSuccess": "Xóa loại agent th<PERSON><PERSON> công", "deleteError": "<PERSON><PERSON> lỗi xảy ra khi xóa loại agent", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t lo<PERSON>i agent thà<PERSON> công"}, "form": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON><PERSON>", "nameCode": "<PERSON><PERSON> đ<PERSON>nh danh", "nameCodePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mã định danh", "instruction": "Hướng dẫn", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> dẫn cho loại agent", "avatar": "Avatar", "avatarUpload": "<PERSON><PERSON><PERSON> lên avatar", "avatarHelp": "Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)", "currentAvatar": "Avatar hi<PERSON>n tại", "currentAvatarNote": "<PERSON><PERSON><PERSON> lên <PERSON>nh mới để thay thế", "modelConfig": "<PERSON><PERSON><PERSON>", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "provider": "Loại Provider", "resources": "<PERSON><PERSON><PERSON>", "model": "Model", "selectModel": "<PERSON><PERSON><PERSON> model", "vectorStore": "Vector Store", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên <PERSON> agent", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả lo<PERSON>i agent", "defaultConfig": "<PERSON><PERSON><PERSON> hình mặc định", "enableProfileCustomization": "<PERSON> phép tùy chỉnh hồ sơ agent", "enableOutputMessenger": "<PERSON> phép xuất ra Messenger", "enableOutputLivechat": "Cho phép xuất ra Website Live Chat", "enableConvert": "<PERSON> chuyển đổi tác vụ", "enableResources": "Sử dụng tài nguyên", "enableStrategy": "<PERSON><PERSON><PERSON><PERSON> thi chiến l<PERSON><PERSON><PERSON> động", "enableMultiAgent": "<PERSON><PERSON><PERSON> t<PERSON>c đa agent", "enableOutputZaloOa": "Cho phép xuất ra Zalo OA", "enableOutputPayment": "Cho phép xuất ra Payment", "enableTool": "<PERSON> phép sử dụng Tool", "enableShipment": "Cho phép xuất ra Shipment", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "draft": "Nháp", "approved": "Đ<PERSON>", "agentSystems": "Agent Systems", "selectAgentSystems": "Chọn agent systems", "agentSystemsConfig": {"title": "Agent Systems", "noSystemsSelected": "Chưa có agent system nào đ<PERSON><PERSON><PERSON> chọn", "systemCount": "<PERSON><PERSON> chọn {{count}} agent system", "addSystem": "Thêm Agent System", "selectedSystems": "Agent Systems đã chọn", "removeSystem": "Xóa agent system", "removeSystemSuccess": "Xóa agent system thành công", "removeSystemError": "Có lỗi xảy ra khi xóa agent system", "confirmDeleteSystem": "Bạn có chắc chắn muốn xóa agent system \"{{systemName}\" khỏi loại agent không?", "deleteSystemWarning": "<PERSON><PERSON><PERSON> động này không thể hoàn tác.", "createdAt": "<PERSON><PERSON><PERSON>"}, "agentSystemSlideIn": {"title": "Chọn Agent System", "close": "Đ<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "system": "<PERSON><PERSON> th<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "createdAt": "<PERSON><PERSON><PERSON>", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "updateSystemsSuccess": "Cập nhật agent systems thành công", "updateSystemsError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật agent systems", "addSystemsToListSuccess": "Thêm agent systems vào danh sách thành công", "cannotSaveInThisMode": "<PERSON><PERSON><PERSON><PERSON> thể lưu ở chế độ này"}, "create": "Tạo <PERSON>i Agent", "creating": "<PERSON><PERSON> tạo loại agent...", "createSuccess": "Tạo loại agent thà<PERSON> công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo lo<PERSON>i agent", "updating": "<PERSON><PERSON> cập nh<PERSON>t lo<PERSON> agent...", "loadAgentSystemsError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách agent systems"}, "validation": {"nameRequired": "Tên loại agent l<PERSON> b<PERSON><PERSON> b<PERSON>", "descriptionRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "statusRequired": "<PERSON>r<PERSON><PERSON> thái là bắ<PERSON> bu<PERSON>c", "agentSystemsRequired": "Ít nhất một agent system là bắt buộc"}, "common": {"confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "error": "Lỗi", "locale": "vi-VN", "success": "<PERSON><PERSON><PERSON><PERSON> công", "loading": "<PERSON><PERSON> tả<PERSON>...", "save": "<PERSON><PERSON><PERSON>", "close": "Đ<PERSON><PERSON>", "edit": "Chỉnh sửa", "view": "Xem", "back": "Quay lại", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "draft": "Nháp", "approved": "Đ<PERSON>", "create": "Tạo", "update": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c"}, "deleteConfirmTitle": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON> lo<PERSON> agent", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa loại agent nà<PERSON>? Hành động này sẽ chuyển loại agent vào thùng rác và có thể khôi phục lại.", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteSuccessMessage": "Loại agent đ<PERSON> đ<PERSON><PERSON><PERSON> xóa thành công", "deleteError": "<PERSON><PERSON> lỗi xảy ra khi xóa loại agent", "selectTypeToDelete": "Chọn loại agent đ<PERSON>a", "deleteWithMigration": "<PERSON>óa với chuyển đổi", "deleteWithMigrationDescription": "Xóa loại agent v<PERSON> chuyển tất cả agents thu<PERSON><PERSON> lo<PERSON> này sang loại mới đư<PERSON><PERSON> chọn.", "newTypeAgent": "Loại agent mới", "selectNewType": "Chọn loại agent mới", "selectNewTypeDescription": "Chọn loại agent mớ<PERSON> để chuyển đổi các agents hi<PERSON><PERSON> tại.", "noAvailableTypes": "<PERSON>hông có loại agent n<PERSON><PERSON> kh<PERSON>c để chuyển đổi. Bạn chỉ có thể xóa mà không chuyển đổi.", "deleteOnly": "Chỉ xóa"}, "list": {"title": "<PERSON><PERSON>", "noAgents": "K<PERSON>ông có Agent nào", "noAgentsDescription": "<PERSON><PERSON><PERSON> tại chưa có Agent nào trong hệ thống.", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Agent. <PERSON><PERSON> lòng thử lại.", "loading": "<PERSON><PERSON> tải danh s<PERSON>ch Agent...", "refreshing": "<PERSON><PERSON> làm mới dữ liệu..."}, "card": {"supervisor": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t viên", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "edit": "Chỉnh sửa", "delete": "Xóa", "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a <PERSON>", "deleteMessage": "Bạn có chắc chắn muốn xóa Agent này không? Hành động này không thể hoàn tác.", "deleteSuccess": "Xóa Agent thành công", "deleteError": "Có lỗi xảy ra khi xóa Agent", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t Agent th<PERSON><PERSON> công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật Agent", "setSupervisor": "Set làm Supervisor", "removeSupervisor": "Bỏ quyền Supervisor", "setSupervisorSuccess": "<PERSON><PERSON> set làm supervisor th<PERSON><PERSON> công", "removeSupervisorSuccess": "<PERSON><PERSON> bỏ quyền supervisor thà<PERSON> công", "supervisorError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi thay đổi quyền supervisor", "restoreSuccess": "<PERSON><PERSON> khôi phục agent thà<PERSON> công", "restoreError": "<PERSON><PERSON> lỗi xảy ra khi khôi phục agent"}, "trash": {"noAgents": "Không có agent nào trong thùng rác", "noAgentsDescription": "<PERSON>h<PERSON><PERSON> rác trống. Các agent đã xóa sẽ xuất hiện ở đây."}, "edit": {"notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy agent"}, "pagination": {"itemsPerPage": "<PERSON><PERSON> mục mỗi trang", "showingItems": "<PERSON><PERSON>n thị {from} - {to} trong tổng số {total} mục", "page": "<PERSON><PERSON>", "of": "c<PERSON>a", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> theo"}, "form": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON>n <PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên agent", "nameCode": "<PERSON><PERSON> đ<PERSON>nh danh", "nameCodePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mã định danh", "instruction": "Hướng dẫn", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> dẫn cho agent", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả agent", "avatar": "Avatar", "avatarUpload": "<PERSON><PERSON><PERSON> lên avatar", "avatarHelp": "Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)", "modelConfig": "<PERSON><PERSON><PERSON>", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "provider": "Loại Provider", "resources": "<PERSON><PERSON><PERSON>", "model": "Model", "selectModel": "<PERSON><PERSON><PERSON> model", "vectorStore": "Vector Store", "selectVectorStore": "Chọn vector store", "mcpSystems": "MCP Systems", "isSupervisor": "Là Supervisor", "create": "Tạo Agent", "creating": "Đang tạo Agent...", "createSuccess": "Tạo Agent thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo Agent", "uploadingAvatar": "<PERSON><PERSON> tải lên avatar...", "uploadAvatarSuccess": "<PERSON><PERSON><PERSON> lên avatar thành công", "uploadAvatarError": "<PERSON><PERSON> lỗi xảy ra khi tải lên avatar", "error": "Lỗi", "errorMessage": "<PERSON><PERSON> lỗi xảy ra khi tạo agent", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật agent"}, "upload": {"success": "Upload thành công", "successMessage": "T<PERSON><PERSON> c<PERSON> file đã đ<PERSON><PERSON><PERSON> upload thành công", "error": "Lỗi upload", "errorMessage": "Có lỗi xảy ra khi upload file"}, "validation": {"nameRequired": "Tên agent <PERSON><PERSON><PERSON><PERSON>", "nameCodeRequired": "<PERSON><PERSON> đ<PERSON>nh danh là b<PERSON><PERSON> buộc", "nameCodeFormat": "<PERSON><PERSON> định danh chỉ đư<PERSON><PERSON> chứa chữ thường, s<PERSON>, dấu gạch dưới và dấu gạch ngang", "instructionRequired": "Hướng dẫn là bắt buộc", "descriptionRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "modelRequired": "<PERSON> <PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>", "modelIdInvalid": "Model ID phải là UUID hợp lệ"}, "mcp": {"config": {"title": "MCP Systems", "description": "Q<PERSON>ản lý MCP Systems cho Agent Type này", "addMCPSystem": "Thêm MCP System", "noMCPSystems": "Chưa có MCP System nào", "removeMCPSystem": "Xóa MCP System", "mcpSystemName": "Tên MCP System", "mcpSystemDescription": "Mô tả MCP System"}, "slideIn": {"title": "Chọn MCP Systems", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "noChangesToSave": "<PERSON><PERSON><PERSON><PERSON> có thay đổi để lưu", "selectAtLeastOne": "<PERSON><PERSON><PERSON> ít nhất một MCP System", "search": "<PERSON><PERSON><PERSON> k<PERSON>m MCP Systems...", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy MCP System nào", "selectAll": "<PERSON><PERSON><PERSON> tất cả", "deselectAll": "Bỏ chọn tất cả", "selected": "<PERSON><PERSON> chọn {{count}} MCP System", "hasChanges": "<PERSON><PERSON> thay đổi", "addMCPsToListSuccess": "<PERSON><PERSON> thêm MCP Systems vào danh sách thành công", "updateMCPsSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t MCP Systems thành công", "updateMCPsError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật MCP Systems", "columns": {"nameServer": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>"}}}}