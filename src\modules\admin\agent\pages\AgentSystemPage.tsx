import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination, SlideInForm } from '@/shared/components/common';
import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { AdminAgentGrid, AddAgentSystemForm, EditAgentSystemForm, AdminMemoriesManagementForm } from '../components';
import { useAdminAgentSystems } from '../agent-system/hooks/useAgentSystem';
import { AgentSystemListItem, AgentStatusEnum, UpdateAgentSystemParams, CreateAgentSystemParams } from '../agent-system/types/agent-system.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { AdminAgentSystemService } from '../agent-system/services/agent-system.service';

// Interface cho dữ liệu từ API
interface ApiAgentSystemItem {
  id: string;
  name: string;
  nameCode: string;
  avatar: string | null;
  model: string;
  provider: string;
  isSupervisor: boolean;
  active: boolean;
}

// Interface cho response từ API
interface ApiAgentSystemResponse {
  items: ApiAgentSystemItem[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}

/**
 * Trang hiển thị danh sách System Agents
 */
const AgentSystemPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const { success} = useSmartNotification();
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');

  // Form states
  const [isCreateFormVisible, setIsCreateFormVisible] = useState(false);
  const [isEditFormVisible, setIsEditFormVisible] = useState(false);
  const [editingAgentId, setEditingAgentId] = useState<string | null>(null);

  // Memories modal states
  const [isMemoriesFormVisible, setIsMemoriesFormVisible] = useState(false);
  const [memoriesAgentId, setMemoriesAgentId] = useState<string | null>(null);

  // Query params
  const queryParams = {
    page,
    limit,
    search: search || '',
  };

  // Lấy danh sách system agents
  const { data: agentsResponse, isLoading, error, refetch } = useAdminAgentSystems(queryParams);



  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleAddAgent = () => {
    setIsCreateFormVisible(true);
  };

  const handleViewTrash = () => {
    navigate('/admin/agent/system/trash');
  };

  // Form handlers
  const hideCreateForm = () => setIsCreateFormVisible(false);
  const hideEditForm = () => {
    setIsEditFormVisible(false);
    setEditingAgentId(null);
  };

  const showEditForm = (agentId: string) => {
    setEditingAgentId(agentId);
    setIsEditFormVisible(true);
  };

  // Memories form handlers
  const showMemoriesForm = (agentId: string) => {
    setMemoriesAgentId(agentId);
    setIsMemoriesFormVisible(true);
  };

  const hideMemoriesForm = () => {
    setIsMemoriesFormVisible(false);
    setMemoriesAgentId(null);
  };

  // Handle form submission
  const handleSubmitCreateAgent = async (values: CreateAgentSystemParams) => {
    try {
      const agentSystemService = new AdminAgentSystemService();
      const response = await agentSystemService.createAgentSystem(values);

      console.log('🔍 [AgentSystemPage] API response:', response);

      // Service trả về response.result, nhưng AddAgentSystemForm expect { result: ... }
      // Nên chúng ta cần wrap lại
      return {
        result: response
      };
    } catch (error) {
      console.error('Error creating agent system:', error);
      throw error;
    }
  };

  // Handle edit form submission
  const handleSubmitEditAgent = async (values: UpdateAgentSystemParams) => {
    if (!editingAgentId) {
      throw new Error('No agent ID for editing');
    }

    try {
      const agentSystemService = new AdminAgentSystemService();
      const response = await agentSystemService.updateAgentSystem(editingAgentId, values);

      console.log('🔍 [AgentSystemPage] Edit API response:', response);

      // Return response theo UpdateAgentSystemResponse interface
      return {
        avatarUrlUpload: response.avatarUrlUpload || ''
      };
    } catch (error) {
      console.error('Error updating agent system:', error);
      throw error;
    }
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Transform dữ liệu từ API thành format phù hợp với component
  const agents = useMemo(() => {
    const response = agentsResponse as ApiAgentSystemResponse | undefined;
    if (!response?.items) {
      return [];
    }

    const apiAgents = response.items;

    // Debug log để kiểm tra dữ liệu từ API
    console.log('🔍 [AgentSystemPage] API Response:', apiAgents);

    return apiAgents.map((apiAgent: ApiAgentSystemItem): AgentSystemListItem => {
      const mappedAgent = {
        id: apiAgent.id,
        name: apiAgent.name,
        nameCode: apiAgent.nameCode,
        avatar: apiAgent.avatar,
        model: apiAgent.model,
        status: apiAgent.active ? AgentStatusEnum.true : AgentStatusEnum.false,
        model_id: apiAgent.model,
        type_provider: apiAgent.provider,
        isSupervisor: apiAgent.isSupervisor,
        active: apiAgent.active,
      };

      // Debug log cho từng agent
      console.log('🔍 [AgentSystemPage] Mapped agent:', {
        id: mappedAgent.id,
        name: mappedAgent.name,
        active: mappedAgent.active,
        isSupervisor: mappedAgent.isSupervisor,
        originalActive: apiAgent.active,
        originalIsSupervisor: apiAgent.isSupervisor
      });

      return mappedAgent;
    });
  }, [agentsResponse]);

  const totalItems = (agentsResponse as ApiAgentSystemResponse | undefined)?.meta?.totalItems || 0;

  // Xử lý reload dữ liệu
  const handleReload = async () => {
    await refetch();
  };

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={[]}
          additionalIcons={[
            {
              icon: 'refresh-cw',
              tooltip: t('common:reload'),
              onClick: handleReload,
              variant: isLoading ? 'primary' : 'default',
            },
            {
              icon: 'trash',
              tooltip: t('admin:agent.system.viewTrash', 'Xem thùng rác'),
              variant: 'default',
              onClick: handleViewTrash,
              className: 'text-gray-600 hover:text-gray-800',
            }
          ]}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={[]}
          additionalIcons={[
            {
              icon: 'refresh-cw',
              tooltip: t('common:reload'),
              onClick: handleReload,
              variant: isLoading ? 'primary' : 'default',
            },
            {
              icon: 'trash',
              tooltip: t('admin:agent.system.viewTrash', 'Xem thùng rác'),
              variant: 'default',
              onClick: handleViewTrash,
              className: 'text-gray-600 hover:text-gray-800',
            }
          ]}
        />
        <EmptyState
          icon="alert-circle"
          title={t('common.error')}
          description={t('admin:agent.list.loadError')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common.retry')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddAgent}
        items={[]}
        additionalIcons={[
          {
            icon: 'refresh-cw',
            tooltip: t('common:reload'),
            onClick: handleReload,
            variant: isLoading ? 'primary' : 'default',
          },
          {
            icon: 'trash',
            tooltip: t('admin:agent.system.viewTrash', 'Xem thùng rác'),
            variant: 'default',
            onClick: handleViewTrash,
            className: 'text-gray-600 hover:text-gray-800',
          }
        ]}
      />

       {/* SlideInForm for Create Agent */}
      <SlideInForm isVisible={isCreateFormVisible} className='mb-4'>
        <AddAgentSystemForm
          onSubmit={handleSubmitCreateAgent}
          onCancel={hideCreateForm}
          onSuccess={() => {
            hideCreateForm();
            success({
              title: t('admin:agent.system.createSuccess', 'Thành công'),
              message: t('admin:agent.system.createSuccessMessage', 'Agent system đã được tạo thành công'),
            });
            refetch(); // Refresh the list
          }}
        />
      </SlideInForm>

      {/* SlideInForm for Edit Agent */}
      <SlideInForm isVisible={isEditFormVisible} className='mb-4'>
        {editingAgentId && (
          <EditAgentSystemForm
            agentId={editingAgentId}
            onSubmit={handleSubmitEditAgent}
            onCancel={hideEditForm}
            onSuccess={() => {
              hideEditForm();
              success({
                title: t('admin:agent.system.updateSuccess', 'Thành công'),
                message: t('admin:agent.system.updateSuccessMessage', 'Agent system đã được cập nhật thành công'),
              });
              refetch(); // Refresh the list
            }}
          />
        )}
      </SlideInForm>

      {/* Memories Form */}
      <SlideInForm isVisible={isMemoriesFormVisible} className='mb-4'>
        {memoriesAgentId && (
          <AdminMemoriesManagementForm
            entityType="agent"
            entityId={memoriesAgentId}
            onCancel={hideMemoriesForm}
            onSuccess={() => {
              hideMemoriesForm();
              success({
                title: t('admin:agent.memories.success', 'Thành công'),
                message: t('admin:agent.memories.successMessage', 'Memories đã được cập nhật thành công'),
              });
              refetch(); // Refresh the list
            }}
          />
        )}
      </SlideInForm>

      {agents.length > 0 ? (
        <>
          <AdminAgentGrid agents={agents} onEditAgent={showEditForm} onMemoriesAgent={showMemoriesForm}  />

          {/* Pagination */}
          {totalItems > limit && (
            <div className="mt-8 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={totalItems}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="cpu"
          title={t('admin:agent.system.noAgents')}
          description={
            search
              ? t('admin:agent.system.noSearchResults')
              : t('admin:agent.system.noAgentsDescription')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleAddAgent}
            >
              {t('admin:agent.system.addAgent')}
            </Button>
          }
        />
      )}

     
    </div>
  );
};

export default AgentSystemPage;
