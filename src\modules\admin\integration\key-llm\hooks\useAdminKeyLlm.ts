/**
 * Hooks cho Admin Key LLM
 */

import { useQuery } from '@tanstack/react-query';
import { AdminKeyLlmService, AdminKeyLlmQueryParams } from '../services/admin-key-llm.service';

/**
 * Query Keys
 */
export const adminKeyLlmQueryKeys = {
  all: ['admin', 'integration', 'key-llm'] as const,
  lists: () => [...adminKeyLlmQueryKeys.all, 'list'] as const,
  list: (params?: AdminKeyLlmQueryParams) => [...adminKeyLlmQueryKeys.lists(), params] as const,
  details: () => [...adminKeyLlmQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...adminKeyLlmQueryKeys.details(), id] as const,
};

/**
 * Hook để lấy danh sách admin key LLMs
 */
export const useAdminKeyLlms = (params?: AdminKeyLlmQueryParams) => {
  return useQuery({
    queryKey: adminKeyLlmQueryKeys.list(params),
    queryFn: () => AdminKeyLlmService.getKeyLlms(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook để lấy chi tiết admin key LLM
 */
export const useAdminKeyLlm = (id: string) => {
  return useQuery({
    queryKey: adminKeyLlmQueryKeys.detail(id),
    queryFn: () => AdminKeyLlmService.getKeyLlm(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};
