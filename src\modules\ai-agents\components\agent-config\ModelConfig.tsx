import {
  Checkbox,
  Icon,
  Select,
  Slider,
  Textarea,
  Typography,
  Card,
  ResponsiveGrid,
} from '@/shared/components/common';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useAgentBasicInfo } from '../../hooks/useAgentBasicInfo';
import {
  useGetRedAIModelsForSelection,
  convertUserModelsToSelectOptions,
  getProviderDisplayName,
  getProviderIcon
} from '../../hooks/useModels';
import {
  useGetUserKeyLlms,
  useGetUserModelsWithKey
} from '../../hooks/useUserModels';

import { ProviderLlmEnum, ModelType } from '../../types';
import { ModelConfigData } from '../../types/model';



interface ModelConfigProps {
  initialData?: ModelConfigData;
  onSave?: (data: ModelConfigData, isValid?: boolean) => void;
  agentId?: string; // Thêm agentId để load và save data
  mode?: 'create' | 'edit'; // Thêm mode để phân biệt create/edit
}

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: ProviderLlmEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: ProviderLlmEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false,
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 border ${
        isSelected
          ? 'border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-1">
        <div
          className={`mr-3 ${
            isSelected
              ? 'text-primary-600 dark:text-primary-400'
              : 'text-gray-700 dark:text-gray-300'
          }`}
        >
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};





// Interface cho System Model response
interface SystemModelItem {
  id: string;
  modelId: string;
  provider: string;
  inputModalities: string[];
  outputModalities: string[];
  samplingParameters: string[]; // Các parameters được hỗ trợ như ["temperature", "top_p"]
  features: string[];
  basePricing: {
    inputRate: number;
    outputRate: number;
  };
  fineTunePricing: {
    inputRate: number;
    outputRate: number;
  };
  trainingPricing: number;
  maxTokens: string; // Thêm maxTokens field
}

// Interface cho Model response (legacy)
interface ModelItem {
  id: string;
  modelId?: string;
  model_id?: string;
  name?: string;
  config?: unknown;
}

/**
 * Component cấu hình model AI cho Agent
 */
const ModelConfig: React.FC<ModelConfigProps> = ({
  initialData,
  onSave,
  agentId,
  mode = 'create',
}) => {
  const { t } = useTranslation(['aiAgents', 'admin', 'common']);

  // API hooks để load basic info
  const { data: basicInfoResponse } = useAgentBasicInfo(agentId && mode === 'edit' ? agentId : '');

  const [configData, setConfigData] = useState<ModelConfigData>(
    initialData || {
      provider: ProviderLlmEnum.OPENAI, // Mặc định sử dụng OpenAI
      modelType: ModelType.REDAI, // Mặc định sử dụng REDAI models
      keyLlmId: 'redai', // FIX: Luôn có giá trị mặc định
      modelId: '', // FIX: Sẽ được auto-select từ API
      temperature: 1,
      topP: 0.5,
      topK: 20,
      maxTokens: 1000, // Thêm maxTokens mặc định
      instruction: '',
    }
  );

  // State để lưu sampling parameters được hỗ trợ bởi model hiện tại
  const [supportedSamplingParams, setSupportedSamplingParams] = useState<string[]>([]);

  // State để lưu selected model data để lấy maxTokens
  const [selectedModel, setSelectedModel] = useState<SystemModelItem | null>(null);

  // State để kiểm soát hiển thị cấu hình nâng cao
  const [showAdvancedConfig, setShowAdvancedConfig] = useState(false);

  // State cho selected provider và key
  const [selectedProvider, setSelectedProvider] = useState<ProviderLlmEnum>(
    ProviderLlmEnum.OPENAI
  );

  // State cho selected key (mặc định chọn RedAI)
  const [selectedKeyId, setSelectedKeyId] = useState<string>('redai');

  // Helper function để filter config data chỉ gửi parameters được hỗ trợ
  const getFilteredConfigData = useCallback(
    (data: ModelConfigData) => {
      const filteredData = { ...data };

      // Chỉ giữ lại sampling parameters được hỗ trợ
      if (!supportedSamplingParams.includes('temperature')) {
        delete (filteredData as Record<string, unknown>)['temperature'];
      }
      if (!supportedSamplingParams.includes('top_p')) {
        delete (filteredData as Record<string, unknown>)['topP'];
      }
      if (!supportedSamplingParams.includes('top_k')) {
        delete (filteredData as Record<string, unknown>)['topK'];
      }

      // Luôn giữ maxTokens vì tất cả model đều hỗ trợ
      // maxTokens sẽ được validate dựa trên maxTokens của model

      return filteredData;
    },
    [supportedSamplingParams]
  );

  // Helper function để validate required fields
  const validateRequiredFields = useCallback((data: ModelConfigData) => {
    const errors: string[] = [];

    if (!data.provider) {
      errors.push('Provider is required');
    }
    if (!data.modelId) {
      errors.push('Model is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }, []);

  // Cập nhật configData khi selectedKeyId thay đổi (chỉ khi cần thiết)
  useEffect(() => {
    const isRedAI = selectedKeyId === 'redai';
    const expectedModelType = isRedAI ? ModelType.REDAI : ModelType.USER;
    const expectedKeyLlmId = isRedAI ? 'redai' : selectedKeyId; // FIX: Luôn có giá trị string

    // Chỉ cập nhật nếu có thay đổi thực sự
    if (configData.modelType !== expectedModelType || configData.keyLlmId !== expectedKeyLlmId) {
      const newConfigData = {
        ...configData,
        modelType: expectedModelType,
        keyLlmId: expectedKeyLlmId,
      };

      setConfigData(newConfigData);

      // Gửi filtered data về parent với validation
      if (onSave) {
        const filteredData = getFilteredConfigData(newConfigData);
        const validation = validateRequiredFields(filteredData);
        onSave(filteredData, validation.isValid);
      }
    }
  }, [selectedKeyId, configData.modelType, configData.keyLlmId, onSave, getFilteredConfigData, validateRequiredFields]); // Thêm dependencies cần thiết

  // Gọi onSave với giá trị mặc định khi component mount lần đầu (chỉ ở mode create)
  useEffect(() => {
    if (mode === 'create' && onSave && !initialData) {
      const filteredData = getFilteredConfigData(configData);
      const validation = validateRequiredFields(filteredData);
      // Gửi filtered data về parent với validation state
      onSave(filteredData, validation.isValid);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getFilteredConfigData]); // Chỉ chạy một lần khi component mount

  // Load data từ API khi có agentId và mode là edit
  useEffect(() => {
    if (basicInfoResponse && mode === 'edit') {
      const apiData = basicInfoResponse;
      const temperature = apiData.modelConfig?.temperature || 1;
      const topP = apiData.modelConfig?.top_p || 0.5;
      const topK = apiData.modelConfig?.top_k || 20;

      // Kiểm tra xem có advanced config không (nếu khác giá trị mặc định thì có advanced config)
      const hasAdvancedConfig = temperature !== 1 || topP !== 0.5 || topK !== 20;

      // Xác định provider và keyLlmId từ API response
      let provider: ProviderLlmEnum = ProviderLlmEnum.OPENAI;
      let modelType: ModelType = ModelType.REDAI;
      let keyLlmId: string = 'redai'; // FIX: Luôn có giá trị string mặc định
      let selectedKey = 'redai'; // Mặc định là RedAI

      // FIX: Xác định provider từ provider field và modelId
      if (apiData.provider) {
        switch (apiData.provider.toUpperCase()) {
          case 'OPENAI':
            provider = ProviderLlmEnum.OPENAI;
            modelType = ModelType.REDAI;
            keyLlmId = 'redai'; // FIX: Sử dụng RedAI
            selectedKey = 'redai';
            break;
          case 'ANTHROPIC':
            provider = ProviderLlmEnum.ANTHROPIC;
            modelType = ModelType.REDAI;
            keyLlmId = 'redai'; // FIX: Sử dụng RedAI
            selectedKey = 'redai';
            break;
          case 'GOOGLE':
            provider = ProviderLlmEnum.GEMINI;
            modelType = ModelType.REDAI;
            keyLlmId = 'redai'; // FIX: Sử dụng RedAI
            selectedKey = 'redai';
            break;
          case 'DEEPSEEK':
            provider = ProviderLlmEnum.DEEPSEEK;
            modelType = ModelType.REDAI;
            keyLlmId = 'redai'; // FIX: Sử dụng RedAI
            selectedKey = 'redai';
            break;
          case 'XAI':
            provider = ProviderLlmEnum.XAI;
            modelType = ModelType.REDAI;
            keyLlmId = 'redai'; // FIX: Sử dụng RedAI
            selectedKey = 'redai';
            break;
          default:
            // SYSTEM_KEY hoặc REDAI: Sử dụng RedAI models
            provider = ProviderLlmEnum.OPENAI;
            modelType = ModelType.REDAI;
            keyLlmId = 'redai'; // FIX: Sử dụng RedAI
            selectedKey = 'redai';
        }
      }

      // Tạm thời comment logic này vì AgentBasicInfoDto chưa có keyLlmId field
      // Sẽ cần cập nhật khi backend API được hoàn thiện
      // if (apiData.keyLlmId) {
      //   modelType = ModelType.USER;
      //   keyLlmId = apiData.keyLlmId;
      //   selectedKey = apiData.keyLlmId;
      // }

      const newConfigData: ModelConfigData = {
        provider: provider,
        modelType: modelType,
        keyLlmId: keyLlmId,
        modelId: apiData.modelId || '', // Sẽ được resolve thành UUID sau khi load models
        temperature: temperature,
        topP: topP,
        topK: topK,
        maxTokens: apiData.modelConfig?.max_tokens || 1000, // Thêm maxTokens từ API
        instruction: apiData.instruction || '',
        showAdvancedConfig: hasAdvancedConfig,
      };

      setConfigData(newConfigData);
      setSelectedProvider(provider);
      setSelectedKeyId(selectedKey);
      setShowAdvancedConfig(hasAdvancedConfig);
    }
  }, [basicInfoResponse, mode]);

  // Preload data cho tất cả providers để tránh giật giao diện
  const { data: openaiModelsResponse, isLoading: isLoadingOpenaiModels } = useGetRedAIModelsForSelection(
    {
      page: 1,
      limit: 100,
      type: ModelType.REDAI,
      provider: ProviderLlmEnum.OPENAI,
    }
  );

  const { data: anthropicModelsResponse, isLoading: isLoadingAnthropicModels } = useGetRedAIModelsForSelection(
    {
      page: 1,
      limit: 100,
      type: ModelType.REDAI,
      provider: ProviderLlmEnum.ANTHROPIC,
    }
  );

  const { data: geminiModelsResponse, isLoading: isLoadingGeminiModels } = useGetRedAIModelsForSelection(
    {
      page: 1,
      limit: 100,
      type: ModelType.REDAI,
      provider: ProviderLlmEnum.GEMINI,
    }
  );

  const { data: deepseekModelsResponse, isLoading: isLoadingDeepseekModels } = useGetRedAIModelsForSelection(
    {
      page: 1,
      limit: 100,
      type: ModelType.REDAI,
      provider: ProviderLlmEnum.DEEPSEEK,
    }
  );

  const { data: xaiModelsResponse, isLoading: isLoadingXaiModels } = useGetRedAIModelsForSelection(
    {
      page: 1,
      limit: 100,
      type: ModelType.REDAI,
      provider: ProviderLlmEnum.XAI,
    }
  );

  // Preload user keys cho tất cả providers
  const { data: openaiUserKeysResponse, isLoading: isLoadingOpenaiUserKeys } = useGetUserKeyLlms(
    {
      provider: ProviderLlmEnum.OPENAI,
      page: 1,
      limit: 100,
    }
  );

  const { data: anthropicUserKeysResponse, isLoading: isLoadingAnthropicUserKeys } = useGetUserKeyLlms(
    {
      provider: ProviderLlmEnum.ANTHROPIC,
      page: 1,
      limit: 100,
    }
  );

  const { data: geminiUserKeysResponse, isLoading: isLoadingGeminiUserKeys } = useGetUserKeyLlms(
    {
      provider: ProviderLlmEnum.GEMINI,
      page: 1,
      limit: 100,
    }
  );

  const { data: deepseekUserKeysResponse, isLoading: isLoadingDeepseekUserKeys } = useGetUserKeyLlms(
    {
      provider: ProviderLlmEnum.DEEPSEEK,
      page: 1,
      limit: 100,
    }
  );

  const { data: xaiUserKeysResponse, isLoading: isLoadingXaiUserKeys } = useGetUserKeyLlms(
    {
      provider: ProviderLlmEnum.XAI,
      page: 1,
      limit: 100,
    }
  );

  // Hook để lấy user models khi có key được chọn (chỉ khi không phải RedAI)
  const { data: userModelsResponse, isLoading: isLoadingUserModels } = useGetUserModelsWithKey(
    selectedKeyId !== 'redai' ? selectedKeyId : undefined,
    selectedProvider,
    {
      page: 1,
      limit: 100,
      type: ModelType.USER,
    }
  );

  // Helper functions để lấy dữ liệu theo provider
  const getRedAIModelsForProvider = useCallback((provider: ProviderLlmEnum) => {
    switch (provider) {
      case ProviderLlmEnum.OPENAI:
        return { data: openaiModelsResponse, isLoading: isLoadingOpenaiModels };
      case ProviderLlmEnum.ANTHROPIC:
        return { data: anthropicModelsResponse, isLoading: isLoadingAnthropicModels };
      case ProviderLlmEnum.GEMINI:
        return { data: geminiModelsResponse, isLoading: isLoadingGeminiModels };
      case ProviderLlmEnum.DEEPSEEK:
        return { data: deepseekModelsResponse, isLoading: isLoadingDeepseekModels };
      case ProviderLlmEnum.XAI:
        return { data: xaiModelsResponse, isLoading: isLoadingXaiModels };
      default:
        return { data: null, isLoading: false };
    }
  }, [
    openaiModelsResponse, isLoadingOpenaiModels,
    anthropicModelsResponse, isLoadingAnthropicModels,
    geminiModelsResponse, isLoadingGeminiModels,
    deepseekModelsResponse, isLoadingDeepseekModels,
    xaiModelsResponse, isLoadingXaiModels
  ]);

  const getUserKeysForProvider = useCallback((provider: ProviderLlmEnum) => {
    switch (provider) {
      case ProviderLlmEnum.OPENAI:
        return { data: openaiUserKeysResponse, isLoading: isLoadingOpenaiUserKeys };
      case ProviderLlmEnum.ANTHROPIC:
        return { data: anthropicUserKeysResponse, isLoading: isLoadingAnthropicUserKeys };
      case ProviderLlmEnum.GEMINI:
        return { data: geminiUserKeysResponse, isLoading: isLoadingGeminiUserKeys };
      case ProviderLlmEnum.DEEPSEEK:
        return { data: deepseekUserKeysResponse, isLoading: isLoadingDeepseekUserKeys };
      case ProviderLlmEnum.XAI:
        return { data: xaiUserKeysResponse, isLoading: isLoadingXaiUserKeys };
      default:
        return { data: null, isLoading: false };
    }
  }, [
    openaiUserKeysResponse, isLoadingOpenaiUserKeys,
    anthropicUserKeysResponse, isLoadingAnthropicUserKeys,
    geminiUserKeysResponse, isLoadingGeminiUserKeys,
    deepseekUserKeysResponse, isLoadingDeepseekUserKeys,
    xaiUserKeysResponse, isLoadingXaiUserKeys
  ]);

  // State cho Models
  const [modelOptions, setModelOptions] = useState<
    { value: string; label: string; data?: unknown }[]
  >([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [modelsError, setModelsError] = useState<string | null>(null);

  // Danh sách providers (bỏ REDAI)
  const providers = [
    { type: ProviderLlmEnum.OPENAI, name: getProviderDisplayName(ProviderLlmEnum.OPENAI) },
    { type: ProviderLlmEnum.ANTHROPIC, name: getProviderDisplayName(ProviderLlmEnum.ANTHROPIC) },
    { type: ProviderLlmEnum.GEMINI, name: getProviderDisplayName(ProviderLlmEnum.GEMINI) },
    { type: ProviderLlmEnum.DEEPSEEK, name: getProviderDisplayName(ProviderLlmEnum.DEEPSEEK) },
    { type: ProviderLlmEnum.XAI, name: getProviderDisplayName(ProviderLlmEnum.XAI) },
  ];

  // Tạo key options với RedAI mặc định dựa trên provider được chọn
  const keyOptions = useMemo(() => {
    const options = [
      {
        value: 'redai',
        label: 'RedAI',
      }
    ];

    // Lấy user keys cho provider hiện tại
    const userKeysData = getUserKeysForProvider(selectedProvider);
    if (userKeysData.data?.result?.items) {
      const userKeyOptions = userKeysData.data.result.items.map(key => ({
        value: key.id,
        label: key.name,
      }));
      options.push(...userKeyOptions);
    }

    return options;
  }, [selectedProvider, getUserKeysForProvider]);

  // Update models when data changes
  useEffect(() => {
    if (selectedKeyId === 'redai') {
      // RedAI models - lấy từ provider được chọn
      const redaiModelsData = getRedAIModelsForProvider(selectedProvider);
      if (redaiModelsData.data?.result?.items) {
        const models = convertUserModelsToSelectOptions(redaiModelsData.data.result.items);
        setModelOptions(models);
        setModelsError(null);
        setIsLoadingModels(redaiModelsData.isLoading);
      } else {
        setModelOptions([]);
        setIsLoadingModels(redaiModelsData.isLoading);
      }
    } else if (selectedKeyId && selectedKeyId !== 'redai' && userModelsResponse?.result?.items) {
      // User models for selected key
      const models = convertUserModelsToSelectOptions(userModelsResponse.result.items);
      setModelOptions(models);
      setModelsError(null);
      setIsLoadingModels(isLoadingUserModels);
    } else {
      setModelOptions([]);
      setIsLoadingModels(isLoadingUserModels);
    }
  }, [selectedKeyId, selectedProvider, getRedAIModelsForProvider, userModelsResponse, isLoadingUserModels]);

  // Set loading state based on API calls
  useEffect(() => {
    const isRedAI = selectedKeyId === 'redai';
    const userKeysData = getUserKeysForProvider(selectedProvider);
    const isLoading = (isRedAI && getRedAIModelsForProvider(selectedProvider).isLoading) ||
                     (!isRedAI && (userKeysData.isLoading || isLoadingUserModels));
    setIsLoadingModels(isLoading);
  }, [selectedKeyId, selectedProvider, getRedAIModelsForProvider, getUserKeysForProvider, isLoadingUserModels]);



  // Fallback options nếu API chưa load hoặc lỗi
  const fallbackOptions = useMemo(
    () => [
      {
        value: 'loading',
        label: isLoadingModels
          ? t('aiAgents:modelConfig.loading')
          : modelsError
            ? t('aiAgents:modelConfig.errorLoadingModel')
            : t('aiAgents:modelConfig.noModel'),
      },
    ],
    [isLoadingModels, modelsError, t]
  );

  // Auto-update model khi có data từ API và model hiện tại không có trong danh sách
  useEffect(() => {
    if (modelOptions.length > 0) {
      const currentModelExists = modelOptions.some(option => option.value === configData.modelId);

      if (!currentModelExists) {
        if (mode === 'create') {
          // Create mode: auto-select first model
          const firstModel = modelOptions[0];
          if (firstModel) {
            // Lấy maxTokens từ model data nếu có
            let defaultMaxTokens = 1000;
            if (firstModel.data && typeof firstModel.data === 'object' && 'maxTokens' in firstModel.data) {
              const modelData = firstModel.data as SystemModelItem;
              const maxTokensLimit = parseInt(modelData.maxTokens) || 8000;
              defaultMaxTokens = Math.min(1000, maxTokensLimit);
            }

            const newConfigData = {
              ...configData,
              modelId: firstModel.value,
              maxTokens: defaultMaxTokens,
            };

            setConfigData(newConfigData);
            // Gửi filtered data về parent với validation
            const filteredData = getFilteredConfigData(newConfigData);
            const validation = validateRequiredFields(filteredData);
            if (onSave) onSave(filteredData, validation.isValid);
          }
        } else if (mode === 'edit' && configData.modelId) {
          // Edit mode: try to find UUID by matching modelId string
          const matchingModel = modelOptions.find(option => {
            if (option.data && typeof option.data === 'object' && 'modelId' in option.data) {
              const modelData = option.data as { modelId: string };
              return modelData.modelId === configData.modelId;
            }
            return false;
          });

          if (matchingModel) {
            console.log('🔄 ModelConfig - Resolved modelId string to UUID:', {
              originalModelId: configData.modelId,
              resolvedUUID: matchingModel.value
            });

            const newConfigData = {
              ...configData,
              modelId: matchingModel.value, // Use UUID instead of string
            };

            setConfigData(newConfigData);
            // Gửi filtered data về parent với validation
            const filteredData = getFilteredConfigData(newConfigData);
            const validation = validateRequiredFields(filteredData);
            if (onSave) onSave(filteredData, validation.isValid);
          }
        }
      }
    }
  }, [modelOptions, configData, onSave, getFilteredConfigData, mode, validateRequiredFields]);

  // Resolve keyLlmId từ string thành UUID khi có userKeys data (chỉ cho edit mode)
  useEffect(() => {
    if (mode === 'edit' && configData.keyLlmId && configData.keyLlmId !== 'redai') {
      // Lấy user keys cho provider hiện tại
      const userKeysData = getUserKeysForProvider(selectedProvider);
      if (userKeysData.data?.result?.items) {
        // Tìm key UUID tương ứng với keyLlmId string
        const matchingKey = userKeysData.data.result.items.find((key: any) => {
          // Có thể match theo name hoặc provider
          return key.name === configData.keyLlmId || key.provider.toLowerCase() === configData.keyLlmId.toLowerCase();
        });

        if (matchingKey && matchingKey.id !== configData.keyLlmId) {
          console.log('🔄 ModelConfig - Resolved keyLlmId string to UUID:', {
            originalKeyLlmId: configData.keyLlmId,
            resolvedUUID: matchingKey.id
          });

          const newConfigData = {
            ...configData,
            keyLlmId: matchingKey.id, // Use UUID instead of string
          };

          setConfigData(newConfigData);
          setSelectedKeyId(matchingKey.id);

          // Gửi filtered data về parent với validation
          const filteredData = getFilteredConfigData(newConfigData);
          const validation = validateRequiredFields(filteredData);
          if (onSave) onSave(filteredData, validation.isValid);
        }
      }
    }
  }, [selectedProvider, getUserKeysForProvider, configData.keyLlmId, mode, onSave, getFilteredConfigData, validateRequiredFields]);

  // Cập nhật supported sampling parameters và selectedModel khi model thay đổi
  useEffect(() => {
    if (configData.modelId && modelOptions.length > 0) {
      const selectedModelOption = modelOptions.find(option => option.value === configData.modelId);
      if (selectedModelOption?.data) {
        const modelData = selectedModelOption.data as SystemModelItem | ModelItem;

        // Kiểm tra xem đây có phải SystemModelItem không
        if ('samplingParameters' in modelData) {
          const systemModel = modelData as SystemModelItem;
          setSupportedSamplingParams(systemModel.samplingParameters || []);
          setSelectedModel(systemModel); // Set selected model để lấy maxTokens
        } else {
          // Fallback cho legacy models - hỗ trợ tất cả parameters
          setSupportedSamplingParams(['temperature', 'top_p', 'top_k']);
          setSelectedModel(null);
        }
      } else {
        // Default fallback
        setSupportedSamplingParams(['temperature', 'top_p', 'top_k']);
        setSelectedModel(null);
      }
    } else {
      setSupportedSamplingParams([]);
      setSelectedModel(null);
    }
  }, [configData.modelId, modelOptions]);

  // Xử lý khi thay đổi select
  const handleSelectChange = (name: string, value: string | number | string[] | number[]) => {
    let newData = {
      ...configData,
      [name]: value,
    };

    // Nếu thay đổi modelId, cần reset maxTokens dựa trên model mới
    if (name === 'modelId' && typeof value === 'string') {
      const selectedModelOption = modelOptions.find(option => option.value === value);
      if (selectedModelOption?.data && typeof selectedModelOption.data === 'object' && 'maxTokens' in selectedModelOption.data) {
        const modelData = selectedModelOption.data as SystemModelItem;
        const maxTokensLimit = parseInt(modelData.maxTokens) || 8000;
        const defaultMaxTokens = Math.min(1000, maxTokensLimit);
        newData = {
          ...newData,
          maxTokens: defaultMaxTokens,
        };
      }
    }

    setConfigData(newData);
    // Gửi filtered data về parent với validation
    const filteredData = getFilteredConfigData(newData);
    const validation = validateRequiredFields(filteredData);
    if (onSave) onSave(filteredData, validation.isValid);
  };

  // Xử lý khi thay đổi slider
  const handleSliderChange = (name: string, value: number) => {
    // Validation đặc biệt cho maxTokens
    if (name === 'maxTokens' && selectedModel?.maxTokens) {
      const maxTokensLimit = parseInt(selectedModel.maxTokens);
      if (value > maxTokensLimit) {
        value = maxTokensLimit; // Giới hạn không vượt quá maxTokens của model
      }
    }

    const newConfigData = {
      ...configData,
      [name]: value,
    };

    setConfigData(newConfigData);
    // Gửi filtered data về parent với validation
    const filteredData = getFilteredConfigData(newConfigData);
    const validation = validateRequiredFields(filteredData);
    if (onSave) onSave(filteredData, validation.isValid);
  };

  // Xử lý khi thay đổi textarea
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const newConfigData = {
      ...configData,
      [name]: value,
    };

    setConfigData(newConfigData);
    // Gửi filtered data về parent với validation
    const filteredData = getFilteredConfigData(newConfigData);
    const validation = validateRequiredFields(filteredData);
    if (onSave) onSave(filteredData, validation.isValid);
  };

  // Xử lý khi thay đổi checkbox
  const handleCheckboxChange = (checked: boolean) => {
    setShowAdvancedConfig(checked);

    // Cập nhật configData với showAdvancedConfig
    // Nếu không check advanced config, sử dụng giá trị mặc định
    let defaultMaxTokens = 1000;
    if (!checked && selectedModel?.maxTokens) {
      const maxTokensLimit = parseInt(selectedModel.maxTokens) || 8000;
      defaultMaxTokens = Math.min(1000, maxTokensLimit);
    }

    const newConfigData = {
      ...configData,
      showAdvancedConfig: checked,
      // Khi không check advanced config, sử dụng giá trị mặc định
      temperature: checked ? configData.temperature : 1,
      topP: checked ? configData.topP : 0.5,
      topK: checked ? configData.topK : 20,
      maxTokens: checked ? configData.maxTokens : defaultMaxTokens,
    };
    setConfigData(newConfigData);
    // Gửi filtered data về parent với validation
    const filteredData = getFilteredConfigData(newConfigData);
    const validation = validateRequiredFields(filteredData);
    if (onSave) onSave(filteredData, validation.isValid);
  };

  // Handle provider selection
  const handleProviderSelect = (provider: ProviderLlmEnum) => {
    setSelectedProvider(provider);

    const isRedAI = selectedKeyId === 'redai';
    const modelType = isRedAI ? ModelType.REDAI : ModelType.USER;

    const newConfigData = {
      ...configData,
      provider,
      modelType: modelType,
      keyLlmId: isRedAI ? 'redai' : configData.keyLlmId, // FIX: Luôn có giá trị string
      modelId: '', // Reset model khi thay đổi provider
    };

    setConfigData(newConfigData);

    // Gửi filtered data về parent với validation
    const filteredData = getFilteredConfigData(newConfigData);
    const validation = validateRequiredFields(filteredData);
    if (onSave) onSave(filteredData, validation.isValid);
  };

  // Handle key LLM selection for USER models
  const handleKeyLlmChange = (keyLlmId: string | number | string[] | number[]) => {
    const selectedKeyLlmId = (Array.isArray(keyLlmId) ? keyLlmId[0] : keyLlmId) as string;
    setSelectedKeyId(selectedKeyLlmId);

    // Xác định modelType và keyLlmId dựa trên selection
    const isRedAI = selectedKeyLlmId === 'redai';
    const modelType = isRedAI ? ModelType.REDAI : ModelType.USER;
    const actualKeyLlmId = isRedAI ? 'redai' : selectedKeyLlmId; // FIX: Luôn có giá trị string

    const newConfigData = {
      ...configData,
      modelType: modelType,
      keyLlmId: actualKeyLlmId,
      modelId: '', // Reset model khi chọn key LLM mới
    };

    setConfigData(newConfigData);
    // Gửi filtered data về parent với validation
    const filteredData = getFilteredConfigData(newConfigData);
    const validation = validateRequiredFields(filteredData);
    if (onSave) onSave(filteredData, validation.isValid);
  };

  return (
    <div>
      <div className="p-4 space-y-6">
        {/* Provider selection (không có RedAI) */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('aiAgents:modelConfig.provider')}
          </Typography>

          <ResponsiveGrid
            maxColumns={{ xs: 2, sm: 3, md: 4, lg: 4, xl: 6 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }}
            gap={{ xs: 3, md: 4, lg: 3 }}
          >
            {providers.map(provider => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
                disabled={false}
              />
            ))}
          </ResponsiveGrid>
        </div>

        {/* Key LLM selection với RedAI mặc định */}
        <div className="mb-6">
          <label
            htmlFor="keyLlmId"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            {t('aiAgents:modelConfig.keyLlm')}
          </label>
          <Select
            options={keyOptions}
            value={selectedKeyId}
            onChange={handleKeyLlmChange}
            placeholder={t('aiAgents:modelConfig.selectKeyLlm')}
            disabled={getUserKeysForProvider(selectedProvider).isLoading}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-1 gap-4 mb-6">
          <div>
            <label
              htmlFor="modelId"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              {t('aiAgents:modelConfig.model')}
            </label>
            <Select
              options={modelOptions.length > 0 ? modelOptions : fallbackOptions}
              value={configData.modelId}
              onChange={value => handleSelectChange('modelId', value)}
              placeholder={t('aiAgents:modelConfig.selectModel')}
              disabled={isLoadingModels}
            />
          </div>


        </div>

        {/* Instruction textarea */}
        <div className="mb-6">
          <label
            htmlFor="instruction"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            {t('aiAgents:modelConfig.instruction')}
          </label>
          <Textarea
            id="instruction"
            name="instruction"
            value={configData.instruction || ''}
            onChange={handleTextareaChange}
            placeholder={t('aiAgents:modelConfig.instructionPlaceholder')}
            className="w-full"
            rows={4}
          />
        </div>

        {/* Advanced configuration checkbox */}
        <div className="mb-4">
          <Checkbox
            label={t('aiAgents:modelConfig.advancedSettings')}
            checked={showAdvancedConfig}
            onChange={handleCheckboxChange}
          />
        </div>

        {/* Advanced configuration sliders - chỉ hiển thị parameters được model hỗ trợ */}
        {showAdvancedConfig && supportedSamplingParams.length > 0 && (
          <div className="space-y-6 border-t pt-4 mt-4">
            {/* Temperature - chỉ hiển thị khi model support */}
            {supportedSamplingParams.includes('temperature') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('aiAgents:modelConfig.temperature')}
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  {t('aiAgents:modelConfig.temperatureDescription')}
                </div>
                <Slider
                  value={configData.temperature}
                  min={0}
                  max={2}
                  step={0.01}
                  onValueChange={(value: number) => handleSliderChange('temperature', value)}
                />
              </div>
            )}

            {/* Top P - chỉ hiển thị khi model support */}
            {supportedSamplingParams.includes('top_p') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('aiAgents:modelConfig.topP')}
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  {t('aiAgents:modelConfig.topPDescription')}
                </div>
                <Slider
                  value={configData.topP}
                  min={0}
                  max={1}
                  step={0.01}
                  onValueChange={(value: number) => handleSliderChange('topP', value)}
                />
              </div>
            )}

            {/* Top K - chỉ hiển thị khi model support */}
            {supportedSamplingParams.includes('top_k') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('aiAgents:modelConfig.topK')}
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  {t('aiAgents:modelConfig.topKDescription')}
                </div>
                <Slider
                  value={configData.topK}
                  min={1}
                  max={50}
                  step={1}
                  onValueChange={(value: number) => handleSliderChange('topK', value)}
                />
              </div>
            )}

            {/* Max Tokens - luôn hiển thị */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('aiAgents:modelConfig.maxTokens', 'Max Tokens')}
              </label>
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                {t('aiAgents:modelConfig.maxTokensDescription', 'Số lượng token tối đa trong response')}
              </div>
              <Slider
                value={configData.maxTokens}
                min={100}
                max={selectedModel?.maxTokens ? parseInt(selectedModel.maxTokens) : 8000}
                step={100}
                onValueChange={(value: number) => handleSliderChange('maxTokens', value)}
              />
              {selectedModel?.maxTokens && (
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {t('aiAgents:modelConfig.maxTokensLimit', 'Model limit')}: {selectedModel.maxTokens}
                </div>
              )}
            </div>

            {/* Hiển thị thông báo nếu không có parameters nào được hỗ trợ */}
            {supportedSamplingParams.length === 0 && (
              <div className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                {t(
                  'aiAgents:modelConfig.noSamplingParameters',
                  'Model này không hỗ trợ tùy chỉnh sampling parameters'
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ModelConfig;
