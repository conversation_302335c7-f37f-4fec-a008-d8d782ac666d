import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Typography, Icon, FormItem } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import type { IconName } from '@/shared/components/common/Icon/Icon';


export interface FileWithMetadata {
  file: File;
  id: string;
  preview?: string;
}

interface MultiFileUploadProps {
  /**
   * Callback khi chọn file
   */
  onChange: (files: FileWithMetadata[]) => void;

  /**
   * Danh sách file đã chọn
   */
  value?: FileWithMetadata[];

  /**
   * Loại file được chấp nhận
   */
  accept?: string;

  /**
   * Label hiển thị
   */
  label?: string;

  /**
   * Text hiển thị khi chưa có file
   */
  placeholder?: string;

  /**
   * Có hiển thị preview không
   */
  showPreview?: boolean;

  /**
   * <PERSON><PERSON> yêu cầu không
   */
  required?: boolean;

  /**
   * Thông báo lỗi
   */
  error?: string;

  /**
   * Chiều cao của vùng kéo thả
   */
  height?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Chỉ chấp nhận ảnh và video
   */
  mediaOnly?: boolean;

  /**
   * Chỉ chấp nhận file văn bản (không cho phép ảnh/video)
   */
  documentsOnly?: boolean;

  /**
   * Translation namespace để sử dụng cho validation messages
   */
  translationNamespace?: string;
}

/**
 * Helper functions cho file icons
 */
const getFileIcon = (fileName: string): IconName => {
  const extension = fileName.split('.').pop()?.toLowerCase() || '';

  if (['txt', 'md', 'rtf'].includes(extension)) return 'file-text';
  if (['json', 'xml', 'yaml', 'yml'].includes(extension)) return 'code';
  if (['pdf'].includes(extension)) return 'file';
  if (['doc', 'docx'].includes(extension)) return 'file-text';
  if (['xls', 'xlsx', 'csv'].includes(extension)) return 'file-spreadsheet';
  if (['ppt', 'pptx'].includes(extension)) return 'presentation';
  if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) return 'image';
  if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) return 'video';
  if (['mp3', 'wav', 'ogg'].includes(extension)) return 'music';
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return 'archive';

  return 'file';
};

const getFileIconColor = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase() || '';

  if (['txt', 'md', 'rtf'].includes(extension)) return '#6b7280'; // gray
  if (['json', 'xml', 'yaml', 'yml'].includes(extension)) return '#10b981'; // emerald
  if (['pdf'].includes(extension)) return '#ef4444'; // red
  if (['doc', 'docx'].includes(extension)) return '#3b82f6'; // blue
  if (['xls', 'xlsx', 'csv'].includes(extension)) return '#22c55e'; // green
  if (['ppt', 'pptx'].includes(extension)) return '#f59e0b'; // amber
  if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) return '#9333ea'; // purple
  if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) return '#ec4899'; // pink
  if (['mp3', 'wav', 'ogg'].includes(extension)) return '#eab308'; // yellow
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return '#6b7280'; // gray

  return '#6b7280'; // default gray
};

/**
 * Component upload nhiều file với drag-and-drop
 */
const MultiFileUpload: React.FC<MultiFileUploadProps> = ({
  onChange,
  value = [],
  accept = '*',
  label,
  placeholder = 'Kéo thả hoặc click để tải lên file',
  showPreview = true,
  required = false,
  error,
  height = 'h-40',
  className = '',
  mediaOnly = false,
  documentsOnly = false,
  translationNamespace = 'data',
}) => {
  const { t } = useTranslation([translationNamespace]);
  const { error: showErrorNotification } = useSmartNotification();
  const [isDragging, setIsDragging] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [containerWidth, setContainerWidth] = useState(800);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  // Kiểm tra khả năng scroll
  const checkScrollability = useCallback(() => {
    if (containerRef.current) {
      const container = containerRef.current;
      const scrollLeft = container.scrollLeft;
      const scrollWidth = container.scrollWidth;
      const clientWidth = container.clientWidth;

      console.log('🔍 Scroll Debug:', {
        scrollLeft,
        scrollWidth,
        clientWidth,
        canScrollHorizontally: scrollWidth > clientWidth,
        totalFiles: value.length
      });

      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1); // -1 để tránh lỗi floating point
    }
  }, [value.length]);

  // Tính toán width container dựa trên viewport
  const calculateContainerWidth = useCallback(() => {
    if (wrapperRef.current) {
      const parentElement = wrapperRef.current.parentElement;
      if (parentElement) {
        const parentWidth = parentElement.clientWidth;
        // Sử dụng tối đa 800px hoặc 90% parent width, tùy theo cái nào nhỏ hơn
        const maxAllowedWidth = Math.min(800, parentWidth * 0.9);
        const calculatedWidth = Math.max(400, maxAllowedWidth);
        setContainerWidth(calculatedWidth);

        console.log('📏 Container width calculated:', {
          parentWidth,
          maxAllowedWidth,
          calculatedWidth,
          windowWidth: window.innerWidth
        });
      }
    }
  }, []);

  // Xử lý scroll ngang
  const handleScrollClick = useCallback((direction: 'left' | 'right') => {
    if (containerRef.current) {
      const container = containerRef.current;
      const scrollAmount = 200; // Số pixel cuộn mỗi lần

      if (direction === 'left') {
        container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
      } else {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      }
    }
  }, []);

  // Kiểm tra scroll khi component mount và khi có thay đổi files
  useEffect(() => {
    // Delay để đảm bảo DOM đã render xong
    const timer = setTimeout(() => {
      calculateContainerWidth();
      checkScrollability();
    }, 200);
    return () => clearTimeout(timer);
  }, [value, checkScrollability, calculateContainerWidth]);

  // Thêm một useEffect khác để kiểm tra khi window resize
  useEffect(() => {
    const handleResize = () => {
      setTimeout(() => {
        calculateContainerWidth();
        checkScrollability();
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [checkScrollability, calculateContainerWidth]);

  // Thêm event listener cho scroll
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollability);
      // Kiểm tra ngay khi mount
      checkScrollability();
      return () => container.removeEventListener('scroll', checkScrollability);
    }
    // Return empty cleanup function nếu không có container
    return () => {};
  }, [checkScrollability]);





  // Xử lý khi kéo file vào vùng upload
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  // Xử lý khi thả file
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  // Xử lý khi chọn file
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // Reset input value để cho phép chọn lại cùng file
    e.target.value = '';
  };

  // Xử lý các file đã chọn
  const processFiles = (fileList: FileList) => {
    console.log(`🔍 Processing ${fileList.length} files...`);
    const newFiles: FileWithMetadata[] = [];
    const rejectedFiles: string[] = [];

    Array.from(fileList).forEach(file => {
      console.log(`📁 Processing file: ${file.name}, type: ${file.type}, documentsOnly: ${documentsOnly}, mediaOnly: ${mediaOnly}`);

      // Nếu documentsOnly = true, từ chối ảnh và video
      if (documentsOnly && (file.type.startsWith('image/') || file.type.startsWith('video/'))) {
        console.log(`🚫 Rejected (documentsOnly): ${file.name}`);
        rejectedFiles.push(file.name);
        return;
      }

      // Nếu mediaOnly = true, chỉ chấp nhận ảnh và video
      if (mediaOnly && !file.type.startsWith('image/') && !file.type.startsWith('video/')) {
        console.log(`🚫 Rejected (mediaOnly): ${file.name}`);
        rejectedFiles.push(file.name);
        return;
      }

      console.log(`✅ Accepted: ${file.name}`);

      // Tạo ID duy nhất cho file
      const fileId = `file-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Tạo preview cho file ảnh và video
      const fileWithMetadata: FileWithMetadata = {
        file,
        id: fileId,
      };

      if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
        fileWithMetadata.preview = URL.createObjectURL(file);
      }

      newFiles.push(fileWithMetadata);
    });

    // Hiển thị thông báo lỗi nếu có file bị từ chối
    if (rejectedFiles.length > 0) {
      console.log(`🚨 Showing notification for ${rejectedFiles.length} rejected files:`, rejectedFiles);

      if (documentsOnly) {
        const rejectedMessage = t(`${translationNamespace}:files.validation.rejectedFiles`, 'Các file bị từ chối: {{files}}', {
          files: rejectedFiles.join(', ')
        });
        const fullMessage = `${t(`${translationNamespace}:files.validation.documentsOnly`, 'Chỉ được phép tải lên file văn bản')}. ${rejectedMessage}`;
        console.log(`📢 Showing documentsOnly notification:`, fullMessage);

        showErrorNotification({
          message: fullMessage,
          duration: 5000,
        });
      } else if (mediaOnly) {
        const rejectedMessage = t(`${translationNamespace}:files.validation.rejectedFiles`, 'Các file bị từ chối: {{files}}', {
          files: rejectedFiles.join(', ')
        });
        const fullMessage = `${t(`${translationNamespace}:files.validation.mediaOnly`, 'Chỉ được phép tải lên ảnh và video')}. ${rejectedMessage}`;
        console.log(`📢 Showing mediaOnly notification:`, fullMessage);

        showErrorNotification({
          message: fullMessage,
          duration: 5000,
        });
      }
    } else {
      console.log(`✅ No rejected files, all ${newFiles.length} files accepted`);
    }

    // Nếu không có file nào được thêm, không gọi callback
    if (newFiles.length === 0) return;

    // Gọi callback với danh sách file mới + file cũ
    onChange([...value, ...newFiles]);

    // Reset input để có thể chọn lại cùng một file
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Xử lý xóa file
  const handleRemoveFile = (fileId: string) => {
    const updatedFiles = value.filter(f => f.id !== fileId);
    onChange(updatedFiles);

    // Giải phóng URL object nếu là preview ảnh
    const fileToRemove = value.find(f => f.id === fileId);
    if (fileToRemove?.preview) {
      URL.revokeObjectURL(fileToRemove.preview);
    }
  };

  // Không cần hàm getFileIcon vì đã sử dụng component FileDisplay

  return (
    <FormItem label={label} required={required} className="w-full">
      <div className={`w-full ${className}`} style={{ width: '100%', maxWidth: '100%', overflow: 'visible' }}>
        {/* Vùng kéo thả - luôn full width */}
        <div
          className={`
            w-full rounded-lg p-4 flex flex-col items-center justify-center relative cursor-pointer border border-dashed border-gray-300 dark:border-gray-700
            ${height}
            ${isDragging ? 'bg-primary-50 dark:bg-primary-900/20 border-primary-400 dark:border-primary-600' : 'bg-gray-50 dark:bg-gray-800/30 hover:bg-gray-100 dark:hover:bg-gray-800/50'}
            ${error ? 'border-red-500' : ''}
          `}
          style={{ width: '100%', maxWidth: '100%' }}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <Icon name="upload" size="lg" className="mb-2 text-gray-400" />
          <Typography variant="body2" className="text-center text-gray-500 dark:text-gray-400">
            {placeholder}
          </Typography>
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            onChange={handleFileChange}
            className="hidden"
            multiple
          />
        </div>

        {/* Danh sách file đã chọn - Tách riêng để không ảnh hưởng drop zone */}
        {showPreview && value.length > 0 && (
          <div className="mt-6 w-full">
            <div
              className="space-y-4 relative"
              style={{
                width: '100%',
                maxWidth: '100%',
                overflow: 'visible',
                zIndex: 10,
                isolation: 'isolate' // Tách riêng layout context
              }}
            >
            <Typography variant="caption" className="font-semibold text-lg mb-2">
              {t(`${translationNamespace}:files.form.selectedFiles`, 'Tệp tin đã chọn')} ({value.length})
            </Typography>

            {/* Hiển thị dạng cuộn ngang cho tất cả file */}
            <div className="w-full" style={{ width: '100%', maxWidth: '100%', overflow: 'visible' }}>
              {/* Scroll buttons - ẩn trên mobile */}
              {canScrollLeft && (
                <button
                  type="button"
                  onClick={() => handleScrollClick('left')}
                  className="absolute left-1 sm:left-2 top-1/2 transform -translate-y-1/2 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full p-1 sm:p-1.5 shadow-md hover:shadow-lg transition-all hover:scale-105 opacity-90 hover:opacity-100 hidden sm:block"
                  title="Cuộn trái"
                >
                  <Icon name="chevron-left" size="sm" className="text-gray-600 dark:text-gray-400" />
                </button>
              )}

              {canScrollRight && (
                <button
                  type="button"
                  onClick={() => handleScrollClick('right')}
                  className="absolute right-1 sm:right-2 top-1/2 transform -translate-y-1/2 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full p-1 sm:p-1.5 shadow-md hover:shadow-lg transition-all hover:scale-105 opacity-90 hover:opacity-100 hidden sm:block"
                  title="Cuộn phải"
                >
                  <Icon name="chevron-right" size="sm" className="text-gray-600 dark:text-gray-400" />
                </button>
              )}

              {/* Horizontal scroll container with fade effects */}
              <div
                ref={wrapperRef}
                className="relative"
                style={{
                  width: `${containerWidth}px`,
                  maxWidth: '100%',
                  contain: 'layout', // Isolate layout để không ảnh hưởng parent
                  flexShrink: 0 // Không cho phép shrink
                }}
              >
                {/* Left fade effect */}
                <div
                  className={`absolute left-0 top-0 bottom-0 w-12 bg-gradient-to-r from-gray-50 via-gray-50/80 to-transparent dark:from-gray-800 dark:via-gray-800/80 dark:to-transparent z-10 pointer-events-none rounded-l-lg transition-opacity duration-300 ${
                    canScrollLeft ? 'opacity-100' : 'opacity-0'
                  }`}
                />

                {/* Right fade effect */}
                <div
                  className={`absolute right-0 top-0 bottom-0 w-12 bg-gradient-to-l from-gray-50 via-gray-50/80 to-transparent dark:from-gray-800 dark:via-gray-800/80 dark:to-transparent z-10 pointer-events-none rounded-r-lg transition-opacity duration-300 ${
                    canScrollRight ? 'opacity-100' : 'opacity-0'
                  }`}
                />

                <div
                  ref={containerRef}
                  className="flex flex-nowrap px-4 gap-2 py-4 rounded-lg bg-gray-50 dark:bg-gray-800 scrollbar-hide relative"
                  style={{
                    minHeight: '140px',
                    maxHeight: '140px',
                    width: '100%', // Sử dụng 100% width của wrapper
                    scrollBehavior: 'smooth',
                    WebkitOverflowScrolling: 'touch',
                    overflowX: 'scroll',
                    overflowY: 'hidden',
                    display: 'flex',
                    flexWrap: 'nowrap',
                    zIndex: 5 // Đảm bảo hiển thị đúng
                  } as React.CSSProperties}
                  onWheel={(e) => {
                    // Enable horizontal scroll with mouse wheel
                    if (e.deltaY !== 0) {
                      e.preventDefault();
                      if (containerRef.current) {
                        containerRef.current.scrollLeft += e.deltaY;
                      }
                    }
                  }}
                >
                {value.map(fileData => (
                  <div
                    key={fileData.id}
                    className="relative group flex-shrink-0 flex flex-col items-center"
                    style={{
                      scrollSnapAlign: 'start'
                    }}
                  >
                    {fileData.preview ? (
                      <>
                        <div className="relative w-24 h-20 rounded-lg overflow-hidden bg-white dark:bg-gray-800 shadow-sm transition-shadow hover:shadow-md">
                          {fileData.file.type.startsWith('video/') ? (
                            <div className="w-full h-full flex items-center justify-center bg-black rounded-lg">
                              <video
                                src={fileData.preview}
                                className="w-full h-full object-cover"
                                controls={false}
                              />
                              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-lg">
                                <Icon name="play" size="sm" className="text-white" />
                              </div>
                            </div>
                          ) : (
                            <img
                              src={fileData.preview}
                              alt={fileData.file.name}
                              className="w-full h-full object-cover"
                              loading="lazy"
                            />
                          )}

                          {/* Remove button - chỉ hiển thị khi hover */}
                          <button
                            type="button"
                            onClick={e => {
                              e.stopPropagation();
                              handleRemoveFile(fileData.id);
                            }}
                            className="absolute top-1 right-1 bg-red-500 hover:bg-red-600 rounded-full p-1 text-white transition-all opacity-0 group-hover:opacity-100 scale-90 group-hover:scale-100"
                            title="Xóa ảnh"
                          >
                            <Icon name="x" size="xs" />
                          </button>
                        </div>
                        {/* Tên file hiển thị bên dưới - làm to hơn */}
                        <div className="mt-2 w-full max-h-16 overflow-hidden">
                          <Typography variant="body2" className="text-gray-700 dark:text-gray-300 truncate text-center font-medium text-sm block">
                            {fileData.file.name.slice(0, 15)}
                          </Typography>
                          <Typography variant="caption" className="text-gray-500 dark:text-gray-400 text-center block text-sm">
                            {(fileData.file.size / 1024).toFixed(1)} KB
                          </Typography>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="relative w-24 h-20 bg-white dark:bg-gray-800 rounded-lg shadow-sm flex flex-col items-center justify-center">
                          {/* Chỉ hiển thị icon, không hiển thị text trong FileDisplay */}
                          <div className="flex items-center justify-center w-full h-full">
                            <Icon
                              name={getFileIcon(fileData.file.name)}
                              size="lg"
                              color={getFileIconColor(fileData.file.name)}
                            />
                          </div>
                          <button
                            type="button"
                            onClick={e => {
                              e.stopPropagation();
                              handleRemoveFile(fileData.id);
                            }}
                            className="absolute top-1 right-1 bg-red-500 hover:bg-red-600 rounded-full p-1 text-white transition-all opacity-0 group-hover:opacity-100 scale-90 group-hover:scale-100"
                            title="Xóa file"
                          >
                            <Icon name="x" size="xs" />
                          </button>
                        </div>
                        {/* Tên file cho non-preview files - làm to hơn */}
                        <div className="mt-2 w-full max-h-16 overflow-hidden">
                          <Typography variant="body2" className="text-gray-700 dark:text-gray-300 truncate text-center font-medium text-sm block">
                            {fileData.file.name}
                          </Typography>
                          <Typography variant="caption" className="text-gray-500 dark:text-gray-400 text-center block text-sm">
                            {(fileData.file.size / 1024).toFixed(1)} KB
                          </Typography>
                        </div>
                      </>
                    )}
                  </div>
                ))}
                </div>
              </div>
              </div>
            </div>
          </div>
        )}

        {/* Error message */}
        {error && (
          <Typography variant="caption" className="text-red-500 mt-2 block">
            {error}
          </Typography>
        )}
      </div>
    </FormItem>
  );
};

export default MultiFileUpload;
