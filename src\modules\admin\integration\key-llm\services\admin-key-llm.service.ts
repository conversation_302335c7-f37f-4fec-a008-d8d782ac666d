/**
 * Admin Key LLM API Service
 */

import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

const API_BASE_URL = '/admin/integration/key-llm';

/**
 * Interface cho Admin Key LLM query parameters
 */
export interface AdminKeyLlmQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  provider?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

/**
 * Interface cho Admin Key LLM response
 */
export interface AdminKeyLlmResponse {
  id: string;
  name: string;
  provider: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

/**
 * Service layer cho admin key LLM operations
 */
export class AdminKeyLlmService {
  /**
   * Lấy danh sách admin key LLMs với phân trang
   */
  static async getKeyLlms(
    params: Partial<AdminKeyLlmQueryParams> = {}
  ): Promise<PaginatedResult<AdminKeyLlmResponse>> {
    try {
      // Validate parameters
      const validatedParams = {
        page: params.page || 1,
        limit: Math.min(params.limit || 10, 100), // Max 100 items per page
        search: params.search?.trim(),
        provider: params.provider,
        sortBy: params.sortBy,
        sortDirection: params.sortDirection,
      };

      // Remove undefined values
      const cleanParams = Object.fromEntries(
        Object.entries(validatedParams).filter(([, value]) => value !== undefined)
      );

      const response = await apiClient.get<PaginatedResult<AdminKeyLlmResponse>>(
        API_BASE_URL,
        { params: cleanParams }
      );

      return response.result;
    } catch (error) {
      console.error('Service error - getKeyLlms:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Lấy thông tin chi tiết một admin key LLM
   */
  static async getKeyLlm(
    id: string
  ): Promise<AdminKeyLlmResponse> {
    try {
      if (!id || id.trim() === '') {
        throw new Error('Key LLM ID is required');
      }

      const response = await apiClient.get<AdminKeyLlmResponse>(
        `${API_BASE_URL}/${id.trim()}`
      );

      return response.result;
    } catch (error) {
      console.error('Service error - getKeyLlm:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Xử lý lỗi chung
   */
  private static handleError(error: unknown): Error {
    if (error instanceof Error) {
      return error;
    }

    if (typeof error === 'object' && error !== null) {
      const errorObj = error as Record<string, unknown>;

      // Handle API response errors
      if (errorObj['response'] && typeof errorObj['response'] === 'object') {
        const response = errorObj['response'] as Record<string, unknown>;
        if (response['data'] && typeof response['data'] === 'object') {
          const data = response['data'] as Record<string, unknown>;
          if (typeof data['message'] === 'string') {
            return new Error(data['message']);
          }
        }
      }

      // Handle network errors
      if (typeof errorObj['message'] === 'string') {
        return new Error(errorObj['message']);
      }
    }

    return new Error('An unknown error occurred');
  }
}

// Export API functions for direct use
export const {
  getKeyLlms,
  getKeyLlm,
} = AdminKeyLlmService;
