import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
  Slider,
  Loading,
  IconCard,
} from '@/shared/components/common';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';
import { apiClient } from '@/shared/api/axios';
import { useAdminSystemModels, AdminSystemModel } from '../hooks/useAdminSystemModels';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import AdminMCPConfig, { MCPConfigData } from './AdminMCPConfig';
import {
  AgentSystemDetail,
  UpdateAgentSystemParams,
  UpdateAgentSystemResponse,
} from '../agent-system/types/agent-system.types';

// Types
interface UpdateAgentSystemDto extends UpdateAgentSystemParams {
  mcpId?: string[];
}


interface EditAgentSystemFormProps {
  agentId: string;
  onSubmit: (values: UpdateAgentSystemDto) => Promise<UpdateAgentSystemResponse>;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false,
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const updateAgentSystemSchema = (t: any) =>
  z.object({
    name: z
      .string()
      .min(1, t('admin:agent.validation.nameRequired', 'Tên agent là bắt buộc'))
      .trim(),
    instruction: z
      .string()
      .min(1, t('admin:agent.validation.instructionRequired', 'Hướng dẫn là bắt buộc'))
      .trim(),
    description: z
      .string()
      .min(1, t('admin:agent.validation.descriptionRequired', 'Mô tả là bắt buộc'))
      .trim(),
    modelId: z
      .string()
      .min(1, t('admin:agent.validation.modelRequired', 'Model là bắt buộc'))
      .uuid(t('admin:agent.validation.modelIdInvalid', 'Model ID phải là UUID hợp lệ')),
  });

const EditAgentSystemForm: React.FC<EditAgentSystemFormProps> = ({
  agentId,
  onSubmit,
  onCancel,
  onSuccess,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const { success: showSuccess, error: showError } = useSmartNotification();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Agent data state
  const [agentData, setAgentData] = useState<AgentSystemDetail | null>(null);

  // Avatar upload states
  const [avatarFiles, setAvatarFiles] = useState<FileWithMetadata[]>([]);

  // Model config states
  const [modelConfig, setModelConfig] = useState<Record<string, number>>({
    temperature: 1,
    top_p: 1,
    top_k: 1,
    max_tokens: 1000,
  });

  // Provider and model selection states
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(
    TypeProviderEnum.OPENAI
  );
  const [selectedModel, setSelectedModel] = useState<AdminSystemModel | null>(null);

  // MCP configuration state
  const [mcpConfigData, setMCPConfigData] = useState<MCPConfigData>({
    mcpSystemIds: [],
  });

  // Fetch system models using hook
  const { data: systemModelsResponse, isLoading: loadingSystemModels } = useAdminSystemModels({
    page: 1,
    limit: 40,
    sortBy: 'systemModels.modelId',
    provider: selectedProvider,
    enabled: true,
  });

  // Load agent detail
  const loadAgentDetail = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get(`/admin/agents/system/${agentId}`, {
        tokenType: 'admin',
      });

      const agent = response.result as AgentSystemDetail;
      setAgentData(agent);

      // Set model config from agent data với fallback values
      if (agent.modelConfig) {
        setModelConfig({
          temperature: agent.modelConfig.temperature ?? 1,
          top_p: agent.modelConfig.top_p ?? 1,
          top_k: agent.modelConfig.top_k ?? 1,
          max_tokens: agent.modelConfig.max_tokens ?? 1000,
        });
      } else {
        // Fallback nếu không có modelConfig
        setModelConfig({
          temperature: 1,
          top_p: 1,
          top_k: 1,
          max_tokens: 1000,
        });
      }

      // Set provider based on agent's provider
      const providerEnum =
        Object.values(TypeProviderEnum).find(
          p => p.toLowerCase() === agent.provider?.toLowerCase()
        ) || TypeProviderEnum.OPENAI;
      setSelectedProvider(providerEnum);

      // Set MCP config data from agent data
      if (agent.mcp && agent.mcp.length > 0) {
        setMCPConfigData({
          mcpSystemIds: agent.mcp.map(mcp => mcp.id),
        });
      }
    } catch (error) {
      console.error('Error loading agent detail:', error);
    } finally {
      setIsLoading(false);
    }
  }, [agentId]);

  // Load vector stores
 

  // Load data on mount
  useEffect(() => {
    loadAgentDetail();
  }, [loadAgentDetail]);

  // Effect để set selectedModel khi có systemModelsResponse và agentData.modelId
  useEffect(() => {
    const modelId = agentData?.modelId || agentData?.model?.id;
    if (systemModelsResponse?.items && modelId) {
      const model = systemModelsResponse.items.find(m => m.id === modelId);
      if (model && !selectedModel) {
        setSelectedModel(model);
        console.log('🔍 [EditAgentSystemForm] Found existing model:', model);
      }
    }
  }, [systemModelsResponse, agentData?.modelId, agentData?.model?.id, selectedModel]);

  // Handle provider selection
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    setSelectedProvider(provider);
    setSelectedModel(null);
    // Reset model config về default
    setModelConfig({
      temperature: 1,
      top_p: 1,
      top_k: 1,
      max_tokens: 1000,
    });
  };

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    const model = systemModelsResponse?.items?.find(m => m.id === modelId);
    setSelectedModel(model || null);

    if (model) {
      // Tạo modelConfig chỉ với các parameters mà model hỗ trợ
      const newModelConfig: Record<string, number> = {};

      if (model.samplingParameters.includes('temperature')) {
        newModelConfig['temperature'] =
          (agentData?.modelConfig as Record<string, number>)?.['temperature'] ?? 1;
      }
      if (model.samplingParameters.includes('top_p')) {
        newModelConfig['top_p'] =
          (agentData?.modelConfig as Record<string, number>)?.['top_p'] ?? 1;
      }
      if (model.samplingParameters.includes('top_k')) {
        newModelConfig['top_k'] =
          (agentData?.modelConfig as Record<string, number>)?.['top_k'] ?? 1;
      }

      // Luôn thêm max_tokens với giá trị từ agentData hoặc mặc định
      const maxTokensLimit = parseInt(model.maxTokens) || 8000;
      const existingMaxTokens = (agentData?.modelConfig as Record<string, number>)?.['max_tokens'] ?? 1000;
      const validMaxTokens = Math.min(existingMaxTokens, maxTokensLimit);
      newModelConfig['max_tokens'] = validMaxTokens;

      setModelConfig(newModelConfig);

      console.log('🔍 [EditAgentSystemForm] Model selected:', {
        modelId: model.modelName,
        samplingParameters: model.samplingParameters,
        newModelConfig,
      });
    }
  };

  // Handle model config changes
  const handleModelConfigChange = (key: string, value: number) => {
    // Validation đặc biệt cho max_tokens
    if (key === 'max_tokens' && selectedModel?.maxTokens) {
      const maxTokensLimit = parseInt(selectedModel.maxTokens);
      if (value > maxTokensLimit) {
        value = maxTokensLimit; // Giới hạn không vượt quá maxTokens của model
      }
    }

    setModelConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle avatar file selection
  const handleAvatarChange = useCallback((files: FileWithMetadata[]) => {
    if (files.length > 0 && files[0]) {
      setAvatarFiles([files[0]]);
    } else {
      setAvatarFiles([]);
    }
  }, []);

  // Handle MCP config save
  const handleMCPConfigSave = useCallback((data: MCPConfigData) => {
    setMCPConfigData(data);
    console.log('🔍 [EditAgentSystemForm] MCP config updated:', data);
  }, []);

  // Upload image file to S3 using presigned URL
  const uploadImageFile = async (file: File, presignedUrl: string) => {
    try {
      console.log('🔍 [uploadImageFile] Starting upload to S3...', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        url: presignedUrl.substring(0, 100) + '...', // Log only first 100 chars for security
      });

      const response = await fetch(presignedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      });

      console.log('🔍 [uploadImageFile] Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
      });

      // S3 PUT thành công thường trả về status 200 hoặc 204
      if (response.ok) {
        console.log('✅ [uploadImageFile] S3 upload successful');
        return true;
      } else {
        throw new Error(`S3 upload failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('❌ [uploadImageFile] Upload error:', error);

      // Kiểm tra nếu lỗi là do CORS nhưng upload thực sự thành công
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.warn('⚠️ [uploadImageFile] CORS error detected, but upload might have succeeded');
        // Trong trường hợp này, chúng ta có thể coi như upload thành công
        // vì S3 presigned URL thường không cho phép đọc response từ cross-origin
        return true;
      }

      throw error;
    }
  };

  // Default values for the form - sử dụng agentData nếu có, nếu không thì empty string
  const defaultValues = React.useMemo(
    () => ({
      name: agentData?.name || '',
      instruction: agentData?.instruction || '',
      description: agentData?.description || '',
      modelId: agentData?.modelId || agentData?.model?.id || '',
    }),
    [agentData]
  );

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    setIsSubmitting(true);

    try {
      // Tạo modelConfig chỉ với các parameters mà model hỗ trợ
      const filteredModelConfig: Record<string, number> = {};

      if (selectedModel) {
        if (
          selectedModel.samplingParameters.includes('temperature') &&
          modelConfig['temperature'] !== undefined
        ) {
          filteredModelConfig['temperature'] = modelConfig['temperature'];
        }
        if (
          selectedModel.samplingParameters.includes('top_p') &&
          modelConfig['top_p'] !== undefined
        ) {
          filteredModelConfig['top_p'] = modelConfig['top_p'];
        }
        if (
          selectedModel.samplingParameters.includes('top_k') &&
          modelConfig['top_k'] !== undefined
        ) {
          filteredModelConfig['top_k'] = modelConfig['top_k'];
        }

        // Luôn thêm max_tokens nếu có
        if (modelConfig['max_tokens'] !== undefined) {
          const maxTokensLimit = parseInt(selectedModel.maxTokens) || 8000;
          // Đảm bảo max_tokens không vượt quá giới hạn của model
          const validMaxTokens = Math.min(modelConfig['max_tokens'], maxTokensLimit);
          filteredModelConfig['max_tokens'] = validMaxTokens;
        }
      }

      console.log('🔍 [EditAgentSystemForm] Filtered modelConfig:', {
        selectedModel: selectedModel?.modelName,
        samplingParameters: selectedModel?.samplingParameters,
        originalModelConfig: modelConfig,
        filteredModelConfig,
      });

      // Prepare form data
      const updateData: Record<string, unknown> = {
        name: values['name'] as string,
        avatarMimeType:
          avatarFiles.length > 0 && avatarFiles[0] ? avatarFiles[0].file.type : undefined,
        modelConfig: filteredModelConfig,
        instruction: values['instruction'] as string,
        description: values['description'] as string,
        mcpId: mcpConfigData.mcpSystemIds.length > 0 ? mcpConfigData.mcpSystemIds : undefined,
      };

      // Chỉ thêm modelId nếu có giá trị và là UUID hợp lệ
      if (values['modelId']) {
        updateData['modelId'] = values['modelId'] as string;
      }

      console.log('Updating agent data:', updateData);

      // Submit form data
      const updateResult = await onSubmit(updateData as unknown as UpdateAgentSystemDto);
      console.log('Agent updated successfully:', updateResult);

      // Upload avatar if provided
      const allUploadPromises: Promise<void>[] = [];

      // Response structure: { code: 200, message: "...", result: { id: "...", avatarUrlUpload: "..." } }
      const avatarUploadUrl = (updateResult as any)?.result?.avatarUrlUpload || (updateResult as any)?.avatarUrlUpload;
      console.log('🔍 Avatar upload URL found:', avatarUploadUrl);

      if (avatarFiles.length > 0 && avatarFiles[0]?.file && avatarUploadUrl) {
        const avatarFile = avatarFiles[0].file;

        // Validate file type
        if (!avatarFile.type.startsWith('image/')) {
          throw new Error('Avatar file must be an image');
        }

        // Validate file size (5MB limit)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (avatarFile.size > maxSize) {
          throw new Error('Avatar file size must be less than 5MB');
        }

        console.log('🔍 Starting avatar upload...');
        console.log('🔍 Avatar file details:', {
          fileName: avatarFile.name,
          fileSize: avatarFile.size,
          fileType: avatarFile.type,
        });

        const avatarUploadPromise = (async () => {
          console.log(`🔍 Uploading avatar:`, {
            fileName: avatarFile.name,
            fileSize: avatarFile.size,
            fileType: avatarFile.type,
          });

          try {
            await uploadImageFile(avatarFile, avatarUploadUrl);
            console.log('✅ Avatar uploaded successfully');
          } catch (error) {
            console.error('❌ Exception uploading avatar:', error);
            throw error;
          }
        })();
        allUploadPromises.push(avatarUploadPromise);
      } else {
        console.log('⚠️ SKIPPING avatar upload:', {
          hasAvatarFiles: avatarFiles.length > 0,
          hasValidFile: !!avatarFiles[0]?.file,
          hasUploadUrl: !!avatarUploadUrl,
          avatarFilesCount: avatarFiles.length,
        });
      }

      // Đợi tất cả uploads hoàn thành
      if (allUploadPromises.length > 0) {
        try {
          await Promise.all(allUploadPromises);
          console.log('🎉 All uploads completed successfully');

          // Hiển thị thông báo thành công cho upload
          showSuccess({
            title: t('admin:agent.upload.success', 'Upload thành công'),
            message: t(
              'admin:agent.upload.successMessage',
              'Tất cả file đã được upload thành công'
            ),
          });
        } catch (uploadError) {
          console.error('❌ Upload error:', uploadError);

          // Hiển thị thông báo lỗi cho upload
          showError({
            title: t('admin:agent.upload.error', 'Lỗi upload'),
            message:
              uploadError instanceof Error
                ? uploadError.message
                : t('admin:agent.upload.errorMessage', 'Có lỗi xảy ra khi upload file'),
          });

          throw new Error(
            `Upload failed: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`
          );
        }
      }

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error updating agent form:', error);

      // Hiển thị thông báo lỗi chung
      showError({
        title: t('admin:agent.form.error', 'Lỗi'),
        message:
          error instanceof Error
            ? error.message
            : t('admin:agent.form.updateError', 'Có lỗi xảy ra khi cập nhật agent'),
      });

      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Danh sách providers
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GEMINI, name: 'Google' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  // Early returns after all hooks
  if (isLoading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </Card>
    );
  }

  if (!agentData) {
    return (
      <Card>
        <div className="text-center py-8">
          <Typography variant="body1" className="text-muted">
            {t('admin:agent.edit.notFound', 'Không tìm thấy agent')}
          </Typography>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div className="flex justify-start items-center mb-6">
        <Typography variant="h4" className="font-semibold">
          {t('admin:agent.system.editAgent', 'Chỉnh sửa Agent System')}
        </Typography>
      </div>

      <Form
        schema={updateAgentSystemSchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
       

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem name="name" label={t('admin:agent.form.name', 'Tên Agent')} required>
              <Input
                fullWidth
                placeholder={t('admin:agent.form.namePlaceholder', 'Nhập tên agent')}
              />
            </FormItem>

           
          </FormGrid>

          <FormItem
            name="instruction"
            label={t('admin:agent.form.instruction', 'Hướng dẫn')}
            required
          >
            <Textarea
              fullWidth
              rows={4}
              placeholder={t('admin:agent.form.instructionPlaceholder', 'Nhập hướng dẫn cho agent')}
            />
          </FormItem>

          <FormItem name="description" label={t('admin:agent.form.description', 'Mô tả')} required>
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('admin:agent.form.descriptionPlaceholder', 'Nhập mô tả agent')}
            />
          </FormItem>
        </div>

        <Divider />

        {/* Avatar Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.avatar', 'Avatar')}
          </Typography>

          {/* Hiển thị avatar hiện tại */}
          {agentData.avatar && (
            <div className="mb-4">
              <Typography variant="body2" className="text-muted mb-2">
                {t('admin:agent.form.currentAvatar', 'Avatar hiện tại')}
              </Typography>
              <img
                src={agentData.avatar}
                alt={agentData.name}
                className="w-16 h-16 rounded-full object-cover border"
              />
            </div>
          )}

          <MultiFileUpload
            label={t('admin:agent.form.avatarUpload', 'Tải lên avatar mới')}
            accept="image/jpeg,image/png"
            placeholder={t('admin:agent.form.avatarHelp', 'Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)')}
            value={avatarFiles}
            onChange={handleAvatarChange}
            mediaOnly={true}
            showPreview={true}
            height="h-32"
          />
        </div>
        <Divider />

        {/* Provider Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.provider', 'Nhà cung cấp')}
          </Typography>

          <div className="flex flex-nowrap gap-3 overflow-x-auto pb-2">
            {providers.map(provider => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
              />
            ))}
          </div>
        </div>

        <Divider />

        {/* Model and Vector Store Selection */}
        <div className="space-y-4">
         

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem name="modelId" label={t('admin:agent.form.model', 'Model')} required>
              <Select
                fullWidth
                placeholder={t('admin:agent.form.selectModel', 'Chọn model')}
                loading={loadingSystemModels}
                options={
                  systemModelsResponse?.items?.map(model => ({
                    value: model.id,
                    label: model.modelName,
                  })) || []
                }
                onChange={value => {
                  handleModelSelect(value as string);
                }}
              />
            </FormItem>

         
          </FormGrid>
        </div>
        <Divider />

        {/* Model Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.modelConfig', 'Cấu hình Model')}
          </Typography>

          {selectedModel ? (
            <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
              {/* Temperature - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('temperature') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.temperature', 'Temperature')}
                  </label>
                  <Slider
                    value={modelConfig['temperature'] || 1}
                    min={0}
                    max={2}
                    step={0.1}
                    onValueChange={(value: number) => handleModelConfigChange('temperature', value)}
                    showValue
                    className="w-full"
                  />
                </div>
              )}

              {/* Top P - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('top_p') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.topP', 'Top P')}
                  </label>
                  <Slider
                    value={modelConfig['top_p'] || 1}
                    min={0}
                    max={1}
                    step={0.1}
                    onValueChange={(value: number) => handleModelConfigChange('top_p', value)}
                    showValue
                    className="w-full"
                  />
                </div>
              )}

              {/* Top K - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('top_k') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.topK', 'Top K')}
                  </label>
                  <Slider
                    value={modelConfig['top_k'] || 1}
                    min={1}
                    max={100}
                    step={1}
                    onValueChange={(value: number) => handleModelConfigChange('top_k', value)}
                    showValue
                    className="w-full"
                  />
                </div>
              )}

              {/* Max Tokens */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-foreground">
                  {t('admin:agent.form.maxTokens', 'Max Tokens')}
                </label>
                <Slider
                  value={modelConfig['max_tokens'] || 1000}
                  min={100}
                  max={selectedModel?.maxTokens ? parseInt(selectedModel.maxTokens) : 8000}
                  step={100}
                  onValueChange={(value: number) => handleModelConfigChange('max_tokens', value)}
                  showValue
                  className="w-full"
                />
                {selectedModel?.maxTokens && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {t('admin:agent.template.maxTokensLimit', 'Model limit')}: {selectedModel.maxTokens}
                  </div>
                )}
              </div>
            </FormGrid>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Typography variant="body2">
                {t(
                  'admin:agent.system.form.selectModelFirst',
                  'Vui lòng chọn model để cấu hình parameters'
                )}
              </Typography>
            </div>
          )}
        </div>

        <Divider />

        {/* MCP Systems Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.mcpSystems', 'MCP Systems')}
          </Typography>

          <AdminMCPConfig initialData={mcpConfigData} onSave={handleMCPConfigSave} mode="edit" />
        </div>

        <Divider />

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-4">
          <IconCard
            icon="x"
            title={t('admin:agent.system.cancel')}
            onClick={onCancel}
            variant="secondary"
            disabled={isSubmitting}
          />
          <IconCard
            icon="check"
            title={t('admin:agent.system.updateAgent')}
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            isLoading={isSubmitting}
          />
        </div>
      </Form>
    </Card>
  );
};

export default EditAgentSystemForm;
